-- 游戏精彩瞬间功能数据库设计
-- 创建时间: 2025-01-07

-- 创建精彩瞬间表
CREATE TABLE IF NOT EXISTS game_moments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '发布用户ID',
    game_id INT NOT NULL COMMENT '游戏ID',
    title VARCHAR(255) NOT NULL COMMENT '瞬间标题',
    description TEXT COMMENT '瞬间描述',
    media_type ENUM('image', 'video') NOT NULL COMMENT '媒体类型',
    media_url VARCHAR(500) NOT NULL COMMENT '媒体文件URL',
    thumbnail_url VARCHAR(500) DEFAULT NULL COMMENT '缩略图URL（视频用）',
    file_size INT DEFAULT NULL COMMENT '文件大小（字节）',
    duration INT DEFAULT NULL COMMENT '视频时长（秒）',
    likes_count INT DEFAULT 0 COMMENT '点赞数',
    dislikes_count INT DEFAULT 0 COMMENT '点踩数',
    comments_count INT DEFAULT 0 COMMENT '评论数',
    views_count INT DEFAULT 0 COMMENT '查看次数',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否精选',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_game_id (game_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at DESC),
    INDEX idx_featured (is_featured, created_at DESC),
    INDEX idx_likes (likes_count DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏精彩瞬间表';

-- 创建瞬间评论表（复用comments表的结构，但独立管理）
CREATE TABLE IF NOT EXISTS moment_comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '评论用户ID',
    moment_id INT NOT NULL COMMENT '瞬间ID',
    content TEXT NOT NULL COMMENT '评论内容',
    parent_id INT DEFAULT NULL COMMENT '父评论ID，NULL表示顶级评论',
    likes_count INT DEFAULT 0 COMMENT '点赞数',
    dislikes_count INT DEFAULT 0 COMMENT '点踩数',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (moment_id) REFERENCES game_moments(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES moment_comments(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_moment_id (moment_id),
    INDEX idx_user_id (user_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_created_at (created_at DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='瞬间评论表';

-- 创建瞬间投票表
CREATE TABLE IF NOT EXISTS moment_votes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    moment_id INT NOT NULL COMMENT '瞬间ID',
    user_id INT NOT NULL COMMENT '投票用户ID',
    vote_type ENUM('like', 'dislike') NOT NULL COMMENT '投票类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (moment_id) REFERENCES game_moments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 唯一约束：每个用户对每个瞬间只能投票一次
    UNIQUE KEY unique_user_moment_vote (user_id, moment_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='瞬间投票表';

-- 创建瞬间评论投票表
CREATE TABLE IF NOT EXISTS moment_comment_votes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    comment_id INT NOT NULL COMMENT '评论ID',
    user_id INT NOT NULL COMMENT '投票用户ID',
    vote_type ENUM('like', 'dislike') NOT NULL COMMENT '投票类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (comment_id) REFERENCES moment_comments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 唯一约束：每个用户对每条评论只能投票一次
    UNIQUE KEY unique_user_comment_vote (user_id, comment_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='瞬间评论投票表';

-- 创建瞬间举报表
CREATE TABLE IF NOT EXISTS moment_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    moment_id INT NOT NULL COMMENT '被举报瞬间ID',
    reporter_user_id INT NOT NULL COMMENT '举报用户ID',
    report_type ENUM('spam', 'harassment', 'inappropriate', 'fake', 'copyright', 'other') NOT NULL COMMENT '举报类型',
    reason TEXT COMMENT '举报原因',
    status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending' COMMENT '处理状态',
    admin_notes TEXT COMMENT '管理员备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (moment_id) REFERENCES game_moments(id) ON DELETE CASCADE,
    FOREIGN KEY (reporter_user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 唯一约束：每个用户对每个瞬间只能举报一次
    UNIQUE KEY unique_moment_report (moment_id, reporter_user_id),
    
    -- 索引
    INDEX idx_status (status, created_at DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='瞬间举报表';

-- 插入一些测试数据
INSERT INTO game_moments (user_id, game_id, title, description, media_type, media_url, thumbnail_url) VALUES
(1, 1, '史诗级BOSS战胜利瞬间', '经过3小时的激战，终于击败了最终BOSS！这个瞬间太激动了！', 'image', '/uploads/moments/epic_boss_victory.jpg', '/uploads/moments/thumbs/epic_boss_victory_thumb.jpg'),
(2, 1, '完美连击演示', '展示一套完美的连击技巧，新手玩家可以学习一下', 'video', '/uploads/moments/perfect_combo.mp4', '/uploads/moments/thumbs/perfect_combo_thumb.jpg'),
(1, 2, '探索隐藏区域', '发现了一个超级隐秘的区域，里面有很多宝藏！', 'image', '/uploads/moments/hidden_area.jpg', '/uploads/moments/thumbs/hidden_area_thumb.jpg');

-- 插入一些测试评论
INSERT INTO moment_comments (user_id, moment_id, content) VALUES
(2, 1, '哇，太厉害了！我打了10次都没过'),
(3, 1, '求攻略！这个BOSS怎么打？'),
(1, 2, '连击太流畅了，学到了！'),
(3, 2, '请问这套连击的按键顺序是什么？');

SELECT 'Game moments system created successfully!' as status;
