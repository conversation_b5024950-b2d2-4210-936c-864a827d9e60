const express = require('express');
const mysql = require('mysql2');
const crypto = require('crypto');
const session = require('express-session');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const multer = require('multer');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3002;

// 中间件配置
app.use(cors({
    origin: ['http://localhost:3001'], // 支持多个前端地址
    credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 添加请求日志中间件
app.use((req, res, next) => {
    const timestamp = new Date().toISOString();
    console.log(`\n📥 [${timestamp}] ${req.method} ${req.url}`);
    console.log(`🔍 IP: ${req.ip}, User-Agent: ${req.get('User-Agent')?.substring(0, 50)}...`);

    // 记录响应
    const originalSend = res.send;
    res.send = function(data) {
        console.log(`📤 [${timestamp}] Response ${res.statusCode} for ${req.method} ${req.url}`);
        return originalSend.call(this, data);
    };

    next();
});

// Session配置
app.use(session({
    secret: process.env.SESSION_SECRET || 'game-review-platform-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // 开发环境设为false
        maxAge: 24 * 60 * 60 * 1000 // 24小时
    }
}));

// 静态文件服务 - 为前端提供服务
app.use(express.static(path.join(__dirname, '../frontend')));

// 游戏图片静态服务
app.use('/images/games', express.static(path.join(__dirname, '../frontend/images/games')));

// 精彩瞬间上传文件静态服务
app.use('/uploads/moments', express.static(path.join(__dirname, '../uploads/moments')));

// 确保上传目录存在
const uploadsDir = path.join(__dirname, '../uploads/moments');
const thumbsDir = path.join(__dirname, '../uploads/moments/thumbs');
if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
}
if (!fs.existsSync(thumbsDir)) {
    fs.mkdirSync(thumbsDir, { recursive: true });
}

// 配置multer用于文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadsDir);
    },
    filename: function (req, file, cb) {
        // 生成唯一文件名
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        cb(null, 'moment-' + uniqueSuffix + ext);
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB
    },
    fileFilter: function (req, file, cb) {
        // 检查文件类型
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/webm'];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('不支持的文件格式'));
        }
    }
});

// 数据库连接配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'game_review_platform',
    charset: 'utf8mb4',
    // MySQL2正确的连接池配置
    connectionLimit: 10,
    queueLimit: 0,
    // 其他配置
    supportBigNumbers: true,
    bigNumberStrings: true,
    dateStrings: true
};

// 创建数据库连接池
const pool = mysql.createPool(dbConfig);
const promisePool = pool.promise();

// 设置连接字符集
pool.on('connection', function (connection) {
    console.log('🔗 新数据库连接建立，设置字符集...');

    // 强制设置字符集为utf8mb4_unicode_ci，并设置SQL模式
    const setupQueries = [
        'SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci',
        'SET character_set_client = utf8mb4',
        'SET character_set_connection = utf8mb4',
        'SET character_set_results = utf8mb4',
        'SET collation_connection = utf8mb4_unicode_ci',
        'SET collation_database = utf8mb4_unicode_ci',
        'SET collation_server = utf8mb4_unicode_ci',
        // 设置SQL模式，避免字符集转换问题
        "SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'"
    ];

    let completed = 0;
    setupQueries.forEach((query, index) => {
        connection.query(query, function(err) {
            completed++;
            if (err) {
                console.error(`设置失败 (${index + 1}/${setupQueries.length}):`, err.message);
            } else {
                console.log(`✅ 设置成功 (${index + 1}/${setupQueries.length}): ${query}`);
            }

            if (completed === setupQueries.length) {
                console.log('🎯 所有数据库设置完成');
            }
        });
    });
});

// 更新评论投票计数的函数
async function updateCommentVoteCounts(commentId) {
    try {
        // 计算点赞数
        const [likesResult] = await promisePool.execute(
            'SELECT COUNT(*) as count FROM comment_votes WHERE comment_id = ? AND vote_type = "like"',
            [commentId]
        );

        // 计算点踩数
        const [dislikesResult] = await promisePool.execute(
            'SELECT COUNT(*) as count FROM comment_votes WHERE comment_id = ? AND vote_type = "dislike"',
            [commentId]
        );

        const likesCount = likesResult[0].count;
        const dislikesCount = dislikesResult[0].count;

        // 更新评论表中的计数
        await promisePool.execute(
            'UPDATE comments SET likes_count = ?, dislikes_count = ? WHERE id = ?',
            [likesCount, dislikesCount, commentId]
        );

        console.log(`✅ 更新评论 ${commentId} 投票计数: 点赞 ${likesCount}, 点踩 ${dislikesCount}`);

        return { likesCount, dislikesCount };
    } catch (error) {
        console.error('更新评论投票计数失败:', error);
        throw error;
    }
}

// 测试数据库连接
async function testConnection() {
    try {
        console.log('🔍 正在测试数据库连接...');
        console.log('数据库配置:', {
            host: dbConfig.host,
            user: dbConfig.user,
            database: dbConfig.database
        });
        const [rows] = await promisePool.execute('SELECT 1 as test');
        console.log('✅ 数据库连接成功');
        console.log('测试查询结果:', rows);

        // 先创建基础表
        await createBaseTables();

        // 再创建精彩瞬间相关表
        await createMomentsTable();
    } catch (error) {
        console.error('❌ 数据库连接失败:', error.message);
        console.error('错误详情:', error);
    }
}

// 创建基础表
async function createBaseTables() {
    try {
        console.log('🎮 正在创建基础表...');

        // 创建用户表
        console.log('📊 创建 users 表...');
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                email VARCHAR(100) NOT NULL UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                avatar_url VARCHAR(255) DEFAULT NULL,
                bio TEXT DEFAULT NULL,
                location VARCHAR(100) DEFAULT NULL,
                last_login_at TIMESTAMP NULL,
                status ENUM('active', 'suspended', 'banned') DEFAULT 'active',
                email_verified BOOLEAN DEFAULT FALSE,
                role ENUM('user', 'admin') DEFAULT 'user' COMMENT '用户角色：user=普通用户，admin=管理员',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_email (email),
                INDEX idx_status (status, created_at),
                INDEX idx_role (role)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        console.log('✅ users 表创建成功');

        // 创建游戏表
        console.log('📊 创建 games 表...');
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS games (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                platform VARCHAR(100) NOT NULL,
                description TEXT,
                cover_url VARCHAR(500),
                avg_rating DECIMAL(3,2) DEFAULT 0.00,
                review_count INT DEFAULT 0,
                genre VARCHAR(100) DEFAULT NULL,
                developer VARCHAR(255) DEFAULT NULL,
                publisher VARCHAR(255) DEFAULT NULL,
                release_date DATE DEFAULT NULL,
                price DECIMAL(10,2) DEFAULT NULL,
                is_featured BOOLEAN DEFAULT FALSE,
                view_count INT DEFAULT 0,
                search_keywords TEXT DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_name (name),
                INDEX idx_avg_rating (avg_rating DESC, review_count DESC),
                INDEX idx_search (name, genre, developer),
                INDEX idx_featured (is_featured, avg_rating DESC)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        console.log('✅ games 表创建成功');

        // 创建评论表
        console.log('📊 创建 comments 表...');
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS comments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                game_id INT NOT NULL,
                rating DECIMAL(2,1) NULL COMMENT '评分，1-5分，回复评论时为NULL',
                content TEXT,
                parent_id INT DEFAULT NULL COMMENT '父评论ID，NULL表示顶级评论',
                is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否已删除',
                likes_count INT DEFAULT 0 COMMENT '点赞数',
                dislikes_count INT DEFAULT 0 COMMENT '点踩数',
                edit_count INT DEFAULT 0,
                last_edited_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE,
                FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
                INDEX idx_game_id (game_id),
                INDEX idx_user_id (user_id),
                INDEX idx_parent_id (parent_id),
                INDEX idx_created_at (created_at DESC),
                INDEX idx_rating (rating DESC, created_at DESC)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        console.log('✅ comments 表创建成功');

        // 创建评论投票表
        console.log('📊 创建 comment_votes 表...');
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS comment_votes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                comment_id INT NOT NULL COMMENT '评论ID',
                user_id INT NOT NULL COMMENT '投票用户ID',
                vote_type ENUM('like', 'dislike') NOT NULL COMMENT '投票类型：like=点赞，dislike=点踩',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_comment_vote (user_id, comment_id) COMMENT '每个用户对每条评论只能投票一次'
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        console.log('✅ comment_votes 表创建成功');

        // 创建评论举报表
        console.log('📊 创建 comment_reports 表...');
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS comment_reports (
                id INT PRIMARY KEY AUTO_INCREMENT,
                comment_id INT NOT NULL,
                reporter_user_id INT NOT NULL,
                report_type ENUM('spam', 'harassment', 'inappropriate', 'fake', 'other') NOT NULL,
                reason TEXT,
                status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
                admin_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE,
                FOREIGN KEY (reporter_user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_report (comment_id, reporter_user_id),
                INDEX idx_status (status, created_at DESC)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        console.log('✅ comment_reports 表创建成功');

        // 创建用户资料表
        console.log('📊 创建 user_profiles 表...');
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS user_profiles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL UNIQUE,
                avatar_url VARCHAR(255) DEFAULT NULL,
                bio TEXT DEFAULT NULL,
                location VARCHAR(100) DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        console.log('✅ user_profiles 表创建成功');

        console.log('✅ 基础表创建完成');

    } catch (error) {
        console.error('❌ 创建基础表失败:', error);
        throw error;
    }
}

// 创建精彩瞬间相关表
async function createMomentsTable() {
    try {
        console.log('🎮 正在创建精彩瞬间相关表...');

        // 创建精彩瞬间表
        console.log('📊 创建 game_moments 表...');
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS game_moments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                game_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                media_type ENUM('image', 'video') NOT NULL,
                media_url VARCHAR(500) NOT NULL,
                thumbnail_url VARCHAR(500) DEFAULT NULL,
                file_size INT DEFAULT NULL,
                duration INT DEFAULT NULL,
                likes_count INT DEFAULT 0,
                dislikes_count INT DEFAULT 0,
                comments_count INT DEFAULT 0,
                views_count INT DEFAULT 0,
                is_featured BOOLEAN DEFAULT FALSE,
                is_deleted BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE,
                INDEX idx_game_id (game_id),
                INDEX idx_user_id (user_id),
                INDEX idx_created_at (created_at DESC)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        console.log('✅ game_moments 表创建成功');

        // 创建瞬间投票表
        console.log('📊 创建 moment_votes 表...');
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS moment_votes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                moment_id INT NOT NULL,
                user_id INT NOT NULL,
                vote_type ENUM('like', 'dislike') NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (moment_id) REFERENCES game_moments(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_moment_vote (user_id, moment_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        console.log('✅ moment_votes 表创建成功');

        // 创建瞬间评论表
        console.log('📊 创建 moment_comments 表...');
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS moment_comments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                moment_id INT NOT NULL,
                content TEXT NOT NULL,
                parent_id INT DEFAULT NULL,
                likes_count INT DEFAULT 0,
                dislikes_count INT DEFAULT 0,
                is_deleted BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (moment_id) REFERENCES game_moments(id) ON DELETE CASCADE,
                FOREIGN KEY (parent_id) REFERENCES moment_comments(id) ON DELETE CASCADE,
                INDEX idx_moment_id (moment_id),
                INDEX idx_user_id (user_id),
                INDEX idx_parent_id (parent_id),
                INDEX idx_created_at (created_at DESC)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        console.log('✅ moment_comments 表创建成功');

        // 创建瞬间评论投票表
        console.log('📊 创建 moment_comment_votes 表...');
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS moment_comment_votes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                comment_id INT NOT NULL,
                user_id INT NOT NULL,
                vote_type ENUM('like', 'dislike') NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (comment_id) REFERENCES moment_comments(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_comment_vote (user_id, comment_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        console.log('✅ moment_comment_votes 表创建成功');

        // 创建瞬间举报表
        console.log('📊 创建 moment_reports 表...');
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS moment_reports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                moment_id INT NOT NULL,
                reporter_user_id INT NOT NULL,
                report_type ENUM('spam', 'harassment', 'inappropriate', 'fake', 'copyright', 'other') NOT NULL,
                reason TEXT,
                status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
                admin_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                UNIQUE KEY unique_moment_report (moment_id, reporter_user_id),
                INDEX idx_status (status, created_at DESC)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        console.log('✅ moment_reports 表创建成功');

        console.log('✅ 精彩瞬间相关表创建成功');

        // 插入一些测试数据
        await insertTestData();

    } catch (error) {
        console.error('❌ 创建精彩瞬间表失败:', error);
    }
}

// 插入测试数据
async function insertTestData() {
    try {
        // 检查是否已有用户数据
        const [existingUsers] = await promisePool.execute('SELECT COUNT(*) as count FROM users');
        if (existingUsers[0].count > 0) {
            console.log('📊 用户表已有数据，跳过基础测试数据插入');
        } else {
            console.log('📊 插入基础测试数据...');

            // 插入测试用户
            await promisePool.execute(`
                INSERT INTO users (username, email, password_hash, bio, role) VALUES
                ('testuser1', '<EMAIL>', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', '游戏爱好者，喜欢各种类型的游戏', 'user'),
                ('testuser2', '<EMAIL>', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', '资深玩家，专注于RPG和动作游戏', 'user'),
                ('gamer_pro', '<EMAIL>', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', '游戏评测达人，客观公正的评价每一款游戏', 'user'),
                ('admin', '<EMAIL>', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', '平台管理员', 'admin')
            `);

            // 插入测试游戏
            await promisePool.execute(`
                INSERT INTO games (name, platform, description, cover_url, avg_rating, review_count, genre, developer, search_keywords) VALUES
                ('塞尔达传说：王国之泪', 'Nintendo Switch', '在这个续作中，林克将踏上全新的冒险旅程，探索海拉鲁王国的天空与大地。拥有全新的能力系统和创造性玩法。', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop', 4.8, 1250, '动作冒险', '任天堂', '塞尔达传说 王国之泪 Nintendo Switch 动作冒险'),
                ('艾尔登法环', 'PC, PS5, Xbox Series X/S', '由宫崎英高和乔治·R·R·马丁共同打造的开放世界动作RPG游戏。在广阔的交界地展开史诗般的冒险。', 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400&h=600&fit=crop', 4.7, 2100, '动作RPG', 'FromSoftware', '艾尔登法环 动作RPG FromSoftware'),
                ('赛博朋克2077', 'PC, PS5, Xbox Series X/S', '在未来的夜之城中体验开放世界的冒险。经过多次更新，游戏体验得到了显著改善。', 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=600&fit=crop', 3.8, 890, '动作RPG', 'CD Projekt RED', '赛博朋克2077 动作RPG CD Projekt RED'),
                ('原神', 'PC, Mobile, PS5', '开放世界冒险游戏，拥有精美的画面和丰富的角色系统。在提瓦特大陆上展开奇幻冒险。', 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=400&h=600&fit=crop', 4.2, 3200, '动作RPG', 'miHoYo', '原神 动作RPG miHoYo'),
                ('我的世界', 'PC, Mobile, Console', '经典的沙盒建造游戏，让玩家在无限的世界中自由创造和探索。', 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?w=400&h=600&fit=crop', 4.6, 5600, '沙盒', 'Mojang Studios', '我的世界 沙盒 Mojang Studios')
            `);

            console.log('✅ 基础测试数据插入成功');
        }

        // 检查是否已有精彩瞬间数据
        const [existingMoments] = await promisePool.execute('SELECT COUNT(*) as count FROM game_moments');
        if (existingMoments[0].count > 0) {
            console.log('📊 精彩瞬间表已有数据，跳过测试数据插入');
            return;
        }

        console.log('📊 插入精彩瞬间测试数据...');

        // 插入测试瞬间
        await promisePool.execute(`
            INSERT INTO game_moments (user_id, game_id, title, description, media_type, media_url, thumbnail_url) VALUES
            (1, 1, '史诗级BOSS战胜利瞬间', '经过3小时的激战，终于击败了最终BOSS！这个瞬间太激动了！', 'image', '/uploads/moments/epic_boss_victory.jpg', '/uploads/moments/thumbs/epic_boss_victory_thumb.jpg'),
            (2, 1, '完美连击演示', '展示一套完美的连击技巧，新手玩家可以学习一下', 'video', '/uploads/moments/perfect_combo.mp4', '/uploads/moments/thumbs/perfect_combo_thumb.jpg'),
            (1, 2, '探索隐藏区域', '发现了一个超级隐秘的区域，里面有很多宝藏！', 'image', '/uploads/moments/hidden_area.jpg', '/uploads/moments/thumbs/hidden_area_thumb.jpg')
        `);

        console.log('✅ 精彩瞬间测试数据插入成功');

    } catch (error) {
        console.error('❌ 插入测试数据失败:', error);
    }
}

// 认证中间件
function requireAuth(req, res, next) {
    console.log('🔐 requireAuth 中间件检查:', {
        hasSession: !!req.session,
        userId: req.session?.userId,
        userStatus: req.session?.userStatus,
        url: req.url
    });

    if (!req.session || !req.session.userId) {
        console.log('❌ 认证失败: 用户未登录');
        return res.status(401).json({ error: '请先登录' });
    }

    // 检查用户状态（如果session中有状态信息）
    if (req.session.userStatus) {
        if (req.session.userStatus === 'banned') {
            console.log('❌ 认证失败: 用户被封禁');
            return res.status(403).json({ error: '您的账户已被封禁，无法执行此操作。' });
        }
        if (req.session.userStatus === 'suspended') {
            console.log('❌ 认证失败: 用户被暂停');
            return res.status(403).json({ error: '您的账户已被暂停，无法执行此操作。' });
        }
    }

    console.log('✅ 认证成功，继续处理请求');
    next();
}

// 增强的认证中间件，实时检查用户状态
async function requireAuthWithStatusCheck(req, res, next) {
    if (!req.session || !req.session.userId) {
        return res.status(401).json({ error: '请先登录' });
    }

    try {
        // 实时查询用户状态
        const [users] = await promisePool.execute(
            'SELECT status FROM users WHERE id = ?',
            [req.session.userId]
        );

        if (users.length === 0) {
            return res.status(401).json({ error: '用户不存在，请重新登录' });
        }

        const userStatus = users[0].status;

        if (userStatus === 'banned') {
            // 清除session
            req.session.destroy();
            return res.status(403).json({ error: '您的账户已被封禁，无法执行此操作。' });
        }

        if (userStatus === 'suspended') {
            // 清除session
            req.session.destroy();
            return res.status(403).json({ error: '您的账户已被暂停，无法执行此操作。' });
        }

        // 更新session中的状态信息
        req.session.userStatus = userStatus;
        next();

    } catch (error) {
        console.error('检查用户状态错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
}

// 调试中间件 - 捕获所有API请求
app.use((req, res, next) => {
    if (req.originalUrl.startsWith('/api/')) {
        console.log('🔍 API路由调试中间件:', {
            method: req.method,
            url: req.url,
            originalUrl: req.originalUrl,
            path: req.path,
            query: req.query,
            hasSession: !!req.session,
            userId: req.session?.userId
        });
    }
    next();
});



// API路由

// 用户注册
app.post('/api/register', async (req, res) => {
    try {
        const { username, email, password } = req.body;
        
        // 基本验证
        if (!username || !email || !password) {
            return res.status(400).json({ error: '用户名、邮箱和密码都是必填项' });
        }
        
        if (password.length < 6) {
            return res.status(400).json({ error: '密码长度至少6位' });
        }
        
        // 检查用户名和邮箱是否已存在
        const [existingUsers] = await promisePool.execute(
            'SELECT id FROM users WHERE username = ? OR email = ?',
            [username, email]
        );
        
        if (existingUsers.length > 0) {
            return res.status(400).json({ error: '用户名或邮箱已存在' });
        }
        
        // 密码哈希
        const passwordHash = crypto.createHash('sha256').update(password + 'game-review-salt').digest('hex');
        
        // 插入新用户
        const [result] = await promisePool.execute(
            'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
            [username, email, passwordHash]
        );
        
        res.status(201).json({
            message: '注册成功',
            userId: result.insertId
        });
        
    } catch (error) {
        console.error('注册错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 用户登录
app.post('/api/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        
        if (!username || !password) {
            return res.status(400).json({ error: '用户名和密码都是必填项' });
        }
        
        // 查找用户（包含状态信息）
        const [users] = await promisePool.execute(
            'SELECT id, username, email, password_hash, role, status FROM users WHERE username = ? OR email = ?',
            [username, username]
        );

        if (users.length === 0) {
            return res.status(401).json({ error: '用户名或密码错误' });
        }

        const user = users[0];

        // 验证密码
        const hashedPassword = crypto.createHash('sha256').update(password + 'game-review-salt').digest('hex');
        const isValidPassword = hashedPassword === user.password_hash;

        if (!isValidPassword) {
            return res.status(401).json({ error: '用户名或密码错误' });
        }

        // 检查用户状态
        if (user.status === 'banned') {
            return res.status(403).json({ error: '您的账户已被封禁，无法登录。如有疑问请联系管理员。' });
        }

        if (user.status === 'suspended') {
            return res.status(403).json({ error: '您的账户已被暂停，无法登录。如有疑问请联系管理员。' });
        }

        // 更新最后登录时间
        await promisePool.execute(
            'UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = ?',
            [user.id]
        );

        // 设置session
        req.session.userId = user.id;
        req.session.username = user.username;
        req.session.userRole = user.role;
        req.session.userStatus = user.status;

        res.json({
            message: '登录成功',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                role: user.role,
                status: user.status
            }
        });
        
    } catch (error) {
        console.error('登录错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 用户登出
app.post('/api/logout', (req, res) => {
    req.session.destroy((err) => {
        if (err) {
            return res.status(500).json({ error: '登出失败' });
        }
        res.json({ message: '登出成功' });
    });
});

// 获取当前用户信息
app.get('/api/user', requireAuth, async (req, res) => {
    try {
        const [users] = await promisePool.execute(
            `SELECT u.id, u.username, u.email, u.role, u.status, u.created_at,
                    p.avatar_url, p.bio, p.location
             FROM users u
             LEFT JOIN user_profiles p ON u.id = p.user_id
             WHERE u.id = ?`,
            [req.session.userId]
        );

        if (users.length === 0) {
            return res.status(404).json({ error: '用户不存在' });
        }

        // 获取用户的评论统计
        const [commentStats] = await promisePool.execute(
            'SELECT COUNT(*) as comment_count, AVG(rating) as avg_rating FROM comments WHERE user_id = ? AND is_deleted = 0 AND rating IS NOT NULL',
            [req.session.userId]
        );



        const user = {
            ...users[0],
            comment_count: parseInt(commentStats[0].comment_count) || 0,
            avg_rating: parseFloat(commentStats[0].avg_rating) || 0
        };

        res.json({ user });

    } catch (error) {
        console.error('获取用户信息错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 获取用户个人资料（公开信息）
app.get('/api/users/:id', async (req, res) => {
    try {
        const userId = parseInt(req.params.id);

        if (isNaN(userId)) {
            return res.status(400).json({ error: '无效的用户ID' });
        }

        const [users] = await promisePool.execute(
            `SELECT u.id, u.username, u.created_at,
                    p.avatar_url, p.bio, p.location
             FROM users u
             LEFT JOIN user_profiles p ON u.id = p.user_id
             WHERE u.id = ?`,
            [userId]
        );

        if (users.length === 0) {
            return res.status(404).json({ error: '用户不存在' });
        }

        // 获取用户的评论统计
        const [commentStats] = await promisePool.execute(
            'SELECT COUNT(*) as comment_count, AVG(rating) as avg_rating FROM comments WHERE user_id = ? AND is_deleted = 0 AND rating IS NOT NULL',
            [userId]
        );

        res.json({
            user: {
                ...users[0],
                comment_count: commentStats[0].comment_count || 0,
                avg_rating: commentStats[0].avg_rating || 0
            }
        });

    } catch (error) {
        console.error('获取用户资料错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 更新用户个人资料
app.put('/api/user/profile', requireAuth, async (req, res) => {
    try {
        const { bio, location, avatar_url } = req.body;
        const userId = req.session.userId;

        // 验证输入长度
        if (bio && bio.length > 500) {
            return res.status(400).json({ error: '个人简介不能超过500字符' });
        }

        if (location && location.length > 100) {
            return res.status(400).json({ error: '地址不能超过100字符' });
        }

        if (avatar_url && avatar_url.length > 255) {
            return res.status(400).json({ error: '头像URL不能超过255字符' });
        }

        // 检查用户资料是否存在，如果不存在则创建
        const [existingProfile] = await promisePool.execute(
            'SELECT id FROM user_profiles WHERE user_id = ?',
            [userId]
        );

        if (existingProfile.length === 0) {
            // 创建新的用户资料
            await promisePool.execute(
                'INSERT INTO user_profiles (user_id, bio, location, avatar_url) VALUES (?, ?, ?, ?)',
                [userId, bio || null, location || null, avatar_url || null]
            );
        } else {
            // 更新现有的用户资料
            await promisePool.execute(
                'UPDATE user_profiles SET bio = ?, location = ?, avatar_url = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?',
                [bio || null, location || null, avatar_url || null, userId]
            );
        }

        res.json({ message: '个人资料更新成功' });

    } catch (error) {
        console.error('更新个人资料错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 获取所有游戏列表（简化版本，先确保基本功能工作）
app.get('/api/games', async (req, res) => {
    try {
        const { search, sortBy = 'name', order = 'ASC' } = req.query;

        let query = 'SELECT id, name, platform, description, cover_url, avg_rating, review_count FROM games';
        let params = [];

        // 搜索功能
        if (search) {
            query += ' WHERE name LIKE ?';
            params.push(`%${search}%`);
        }

        // 排序
        const validSortFields = ['name', 'avg_rating', 'review_count', 'created_at'];
        const validOrder = ['ASC', 'DESC'];

        if (validSortFields.includes(sortBy) && validOrder.includes(order.toUpperCase())) {
            // 对于中文游戏名称，使用拼音排序
            if (sortBy === 'name') {
                // 使用 CONVERT 函数将中文转换为拼音进行排序
                query += ` ORDER BY CONVERT(${sortBy} USING gbk) ${order.toUpperCase()}`;
            } else {
                query += ` ORDER BY ${sortBy} ${order.toUpperCase()}`;
            }
        }

        console.log('执行查询:', query);
        console.log('参数:', params);

        const [games] = await promisePool.execute(query, params);

        res.json({ games });

    } catch (error) {
        console.error('获取游戏列表错误:', error);
        console.error('错误详情:', error.message);
        console.error('错误堆栈:', error.stack);
        res.status(500).json({ error: '服务器内部错误', details: error.message });
    }
});

// 获取单个游戏详情
app.get('/api/games/:id', async (req, res) => {
    try {
        const gameId = parseInt(req.params.id);

        if (isNaN(gameId)) {
            return res.status(400).json({ error: '无效的游戏ID' });
        }

        // 获取游戏信息
        const [games] = await promisePool.execute(
            'SELECT id, name, platform, description, cover_url, avg_rating, review_count, created_at FROM games WHERE id = ?',
            [gameId]
        );

        if (games.length === 0) {
            return res.status(404).json({ error: '游戏不存在' });
        }

        res.json({ game: games[0] });

    } catch (error) {
        console.error('获取游戏详情错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 获取游戏评论（包含回复和投票信息）
app.get('/api/games/:id/comments', async (req, res) => {
    try {
        const gameId = parseInt(req.params.id);
        const userId = req.session.userId; // 当前用户ID，用于获取投票状态

        if (isNaN(gameId)) {
            return res.status(400).json({ error: '无效的游戏ID' });
        }

        // 获取所有评论（包括回复）
        const [comments] = await promisePool.execute(
            `SELECT
                c.id,
                c.game_id,
                c.user_id,
                u.username,
                c.rating,
                c.content,
                c.parent_id,
                c.likes_count,
                c.dislikes_count,
                c.created_at,
                c.updated_at,
                (SELECT COUNT(*) FROM comments replies WHERE replies.parent_id = c.id AND replies.is_deleted = 0) as replies_count
            FROM comments c
            JOIN users u ON c.user_id = u.id
            WHERE c.game_id = ? AND c.is_deleted = 0
            ORDER BY c.parent_id ASC, c.created_at ASC`,
            [gameId]
        );

        // 如果用户已登录，获取用户的投票状态
        let userVotes = {};
        if (userId && comments.length > 0) {
            const placeholders = comments.map(() => '?').join(',');
            const [votes] = await promisePool.execute(
                `SELECT comment_id, vote_type
                 FROM comment_votes
                 WHERE user_id = ? AND comment_id IN (${placeholders})`,
                [userId, ...comments.map(c => c.id)]
            );

            userVotes = votes.reduce((acc, vote) => {
                acc[vote.comment_id] = vote.vote_type;
                return acc;
            }, {});
        }

        // 组织评论层级结构
        const topLevelComments = comments.filter(c => !c.parent_id);
        const repliesMap = comments.filter(c => c.parent_id).reduce((acc, reply) => {
            if (!acc[reply.parent_id]) acc[reply.parent_id] = [];
            acc[reply.parent_id].push({
                ...reply,
                userVote: userVotes[reply.id] || null
            });
            return acc;
        }, {});

        const commentsWithReplies = topLevelComments.map(comment => ({
            ...comment,
            userVote: userVotes[comment.id] || null,
            replies: repliesMap[comment.id] || []
        }));

        res.json({ comments: commentsWithReplies });

    } catch (error) {
        console.error('获取游戏评论错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 发布游戏评论
app.post('/api/comments', requireAuthWithStatusCheck, async (req, res) => {
    try {
        const { gameId, rating, content } = req.body;
        const userId = req.session.userId;

        // 验证输入
        if (!gameId || !rating) {
            return res.status(400).json({ error: '游戏ID和评分是必填项' });
        }

        if (rating < 1 || rating > 5) {
            return res.status(400).json({ error: '评分必须在1-5之间' });
        }

        // 检查游戏是否存在
        const [games] = await promisePool.execute(
            'SELECT id FROM games WHERE id = ?',
            [gameId]
        );

        if (games.length === 0) {
            return res.status(404).json({ error: '游戏不存在' });
        }

        // 检查用户是否已经评论过这个游戏
        const [existingComments] = await promisePool.execute(
            'SELECT id FROM comments WHERE user_id = ? AND game_id = ?',
            [userId, gameId]
        );

        if (existingComments.length > 0) {
            return res.status(400).json({ error: '您已经评论过这个游戏了' });
        }

        // 插入评论
        const [result] = await promisePool.execute(
            'INSERT INTO comments (user_id, game_id, rating, content) VALUES (?, ?, ?, ?)',
            [userId, gameId, rating, content || '']
        );

        // 更新游戏的平均评分和评论数量
        await updateGameRating(gameId);

        res.status(201).json({
            message: '评论发布成功',
            commentId: result.insertId
        });

    } catch (error) {
        console.error('发布评论错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 更新游戏评分的辅助函数
async function updateGameRating(gameId) {
    try {
        // 计算平均评分（只计算有评分的顶级评论）
        const [ratingStats] = await promisePool.execute(
            'SELECT AVG(rating) as avg_rating FROM comments WHERE game_id = ? AND is_deleted = 0 AND rating IS NOT NULL AND parent_id IS NULL',
            [gameId]
        );

        // 计算评论数量（只计算顶级评论，不包括回复）
        const [countStats] = await promisePool.execute(
            'SELECT COUNT(*) as review_count FROM comments WHERE game_id = ? AND is_deleted = 0 AND parent_id IS NULL',
            [gameId]
        );

        const avgRating = ratingStats[0].avg_rating ? parseFloat(ratingStats[0].avg_rating).toFixed(2) : 0;
        const reviewCount = countStats[0].review_count;

        await promisePool.execute(
            'UPDATE games SET avg_rating = ?, review_count = ? WHERE id = ?',
            [avgRating, reviewCount, gameId]
        );

        console.log(`✅ 更新游戏 ${gameId} 统计: 平均评分 ${avgRating}, 评论数 ${reviewCount}`);

    } catch (error) {
        console.error('更新游戏评分错误:', error);
    }
}

// 删除评论API
app.delete('/api/comments/:id', requireAuthWithStatusCheck, async (req, res) => {
    try {
        const commentId = parseInt(req.params.id);
        const userId = req.session.userId;

        if (isNaN(commentId)) {
            return res.status(400).json({ error: '无效的评论ID' });
        }

        // 检查评论是否存在且属于当前用户
        const [comments] = await promisePool.execute(
            'SELECT id, user_id, game_id FROM comments WHERE id = ? AND is_deleted = 0',
            [commentId]
        );

        if (comments.length === 0) {
            return res.status(404).json({ error: '评论不存在' });
        }

        if (comments[0].user_id !== userId) {
            return res.status(403).json({ error: '您只能删除自己的评论' });
        }

        // 软删除评论（标记为已删除，不真正删除）
        await promisePool.execute(
            'UPDATE comments SET is_deleted = 1 WHERE id = ?',
            [commentId]
        );

        // 更新游戏评分统计
        await updateGameRating(comments[0].game_id);

        res.json({ message: '评论删除成功' });

    } catch (error) {
        console.error('删除评论错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 评论投票API
app.post('/api/comments/:id/vote', requireAuthWithStatusCheck, async (req, res) => {
    try {
        const commentId = parseInt(req.params.id);
        const userId = req.session.userId;
        const { voteType } = req.body; // 'like' 或 'dislike'

        if (isNaN(commentId)) {
            return res.status(400).json({ error: '无效的评论ID' });
        }

        if (!['like', 'dislike'].includes(voteType)) {
            return res.status(400).json({ error: '无效的投票类型' });
        }

        // 检查评论是否存在
        const [comments] = await promisePool.execute(
            'SELECT id FROM comments WHERE id = ? AND is_deleted = 0',
            [commentId]
        );

        if (comments.length === 0) {
            return res.status(404).json({ error: '评论不存在' });
        }

        // 检查用户是否已经投票
        const [existingVotes] = await promisePool.execute(
            'SELECT vote_type FROM comment_votes WHERE user_id = ? AND comment_id = ?',
            [userId, commentId]
        );

        if (existingVotes.length > 0) {
            if (existingVotes[0].vote_type === voteType) {
                // 取消投票
                await promisePool.execute(
                    'DELETE FROM comment_votes WHERE user_id = ? AND comment_id = ?',
                    [userId, commentId]
                );

                // 更新评论投票计数
                await updateCommentVoteCounts(commentId);

                res.json({ message: '投票已取消', action: 'removed' });
            } else {
                // 更改投票类型
                await promisePool.execute(
                    'UPDATE comment_votes SET vote_type = ? WHERE user_id = ? AND comment_id = ?',
                    [voteType, userId, commentId]
                );

                // 更新评论投票计数
                await updateCommentVoteCounts(commentId);

                res.json({ message: '投票已更新', action: 'updated' });
            }
        } else {
            // 新增投票
            await promisePool.execute(
                'INSERT INTO comment_votes (user_id, comment_id, vote_type) VALUES (?, ?, ?)',
                [userId, commentId, voteType]
            );

            // 更新评论投票计数
            await updateCommentVoteCounts(commentId);

            // 创建投票通知
            const [comment] = await promisePool.execute(
                'SELECT user_id FROM comments WHERE id = ?',
                [commentId]
            );

            if (comment.length > 0 && comment[0].user_id !== userId) {
                const [voter] = await promisePool.execute(
                    'SELECT username FROM users WHERE id = ?',
                    [userId]
                );

                if (voter.length > 0) {
                    const voteText = voteType === 'like' ? '点赞' : '点踩';
                    await createNotification(
                        comment[0].user_id,
                        voteType,
                        `${voter[0].username} ${voteText}了您的评论`,
                        `您的评论获得了一个${voteText}`,
                        commentId,
                        'comment',
                        userId
                    );
                }
            }

            res.json({ message: '投票成功', action: 'added' });
        }

    } catch (error) {
        console.error('投票错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 获取单个评论信息API
app.get('/api/comments/:id', async (req, res) => {
    try {
        const commentId = parseInt(req.params.id);

        if (isNaN(commentId)) {
            return res.status(400).json({ error: '无效的评论ID' });
        }

        const [comments] = await promisePool.execute(`
            SELECT c.*, u.username, g.name as game_name
            FROM comments c
            LEFT JOIN users u ON c.user_id = u.id
            LEFT JOIN games g ON c.game_id = g.id
            WHERE c.id = ? AND c.is_deleted = 0
        `, [commentId]);

        if (comments.length === 0) {
            return res.status(404).json({ error: '评论不存在' });
        }

        res.json(comments[0]);
    } catch (error) {
        console.error('获取评论信息错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 回复评论API
app.post('/api/comments/:id/reply', requireAuthWithStatusCheck, async (req, res) => {
    try {
        const parentId = parseInt(req.params.id);
        const userId = req.session.userId;
        const { content } = req.body;

        if (isNaN(parentId)) {
            return res.status(400).json({ error: '无效的评论ID' });
        }

        if (!content || content.trim().length === 0) {
            return res.status(400).json({ error: '回复内容不能为空' });
        }

        if (content.length > 500) {
            return res.status(400).json({ error: '回复内容不能超过500字符' });
        }

        // 检查父评论是否存在
        const [parentComments] = await promisePool.execute(
            'SELECT id, game_id FROM comments WHERE id = ? AND is_deleted = 0',
            [parentId]
        );

        if (parentComments.length === 0) {
            return res.status(404).json({ error: '要回复的评论不存在' });
        }

        const gameId = parentComments[0].game_id;

        console.log('📝 准备插入回复评论:', {
            userId,
            gameId,
            content: content.trim(),
            parentId,
            contentLength: content.trim().length
        });

        // 插入回复评论（rating为NULL，因为回复不需要评分）
        let result;
        try {
            // 临时禁用外键检查
            console.log('🔧 禁用外键检查...');
            await promisePool.execute('SET FOREIGN_KEY_CHECKS = 0');

            // 在插入前强制设置字符集
            console.log('🔧 设置字符集...');
            await promisePool.execute('SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci');
            await promisePool.execute('SET collation_connection = utf8mb4_unicode_ci');

            console.log('🔧 尝试插入回复评论（无外键检查）...');
            [result] = await promisePool.execute(
                'INSERT INTO comments (user_id, game_id, content, parent_id) VALUES (?, ?, ?, ?)',
                [userId, gameId, content.trim(), parentId]
            );
            console.log('✅ 回复评论插入成功，ID:', result.insertId);

            // 重新启用外键检查
            await promisePool.execute('SET FOREIGN_KEY_CHECKS = 1');
            console.log('✅ 外键检查已重新启用');

        } catch (insertError) {
            console.error('❌ 插入回复评论失败:', insertError);
            console.error('插入参数:', { userId, gameId, content: content.trim(), parentId });

            // 重新启用外键检查
            try {
                await promisePool.execute('SET FOREIGN_KEY_CHECKS = 1');
            } catch (e) {
                console.error('重新启用外键检查失败:', e);
            }

            // 尝试使用原始SQL（不使用预处理语句）
            console.log('🔄 尝试原始SQL插入...');
            try {
                const mysql = require('mysql2');
                const escapedContent = mysql.escape(content.trim());
                const rawSql = `INSERT INTO comments (user_id, game_id, content, parent_id) VALUES (${userId}, ${gameId}, ${escapedContent}, ${parentId})`;
                console.log('🔍 原始SQL:', rawSql);

                [result] = await promisePool.query(rawSql);
                console.log('✅ 原始SQL插入成功，ID:', result.insertId);
            } catch (rawError) {
                console.error('❌ 原始SQL也失败:', rawError);
                throw rawError;
            }
        }

        // 创建回复通知（使用应用代码，避免触发器字符集问题）
        console.log('🔔 开始创建回复通知...');

        const [parentComment] = await promisePool.execute(
            'SELECT user_id FROM comments WHERE id = ?',
            [parentId]
        );

        if (parentComment.length > 0 && parentComment[0].user_id !== userId) {
            console.log('✅ 需要创建通知，获取回复者信息...');

            const [replier] = await promisePool.execute(
                'SELECT username FROM users WHERE id = ?',
                [userId]
            );

            if (replier.length > 0) {
                console.log('🔔 调用createNotification函数...');

                try {
                    await createNotification(
                        parentComment[0].user_id,
                        'reply',
                        `${replier[0].username} 回复了您的评论`,
                        `回复内容：${content.length > 100 ? content.substring(0, 100) + '...' : content}`,
                        result.insertId,
                        'comment',
                        userId
                    );
                    console.log('✅ 回复通知创建成功');
                } catch (notificationError) {
                    console.error('❌ 创建回复通知失败:', notificationError);
                    // 不要因为通知失败而影响回复功能
                }
            }
        } else {
            console.log('ℹ️ 无需创建通知（自己回复自己或父评论不存在）');
        }

        // 获取新插入的回复信息
        const [newReply] = await promisePool.execute(
            `SELECT c.id, c.content, c.created_at, c.likes_count, c.dislikes_count, u.username
             FROM comments c
             JOIN users u ON c.user_id = u.id
             WHERE c.id = ?`,
            [result.insertId]
        );

        res.status(201).json({
            message: '回复成功',
            reply: {
                ...newReply[0],
                user_id: userId,
                parent_id: parentId,
                userVote: null,
                replies_count: 0
            }
        });

    } catch (error) {
        console.error('回复评论错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 编辑评论
app.put('/api/comments/:id', requireAuthWithStatusCheck, async (req, res) => {
    try {
        const commentId = parseInt(req.params.id);
        const { content, rating } = req.body;
        const userId = req.session.userId;

        if (isNaN(commentId)) {
            return res.status(400).json({ error: '无效的评论ID' });
        }

        if (!content || content.trim().length === 0) {
            return res.status(400).json({ error: '评论内容不能为空' });
        }

        // 检查评论是否存在且属于当前用户
        const [comments] = await promisePool.execute(
            'SELECT id, user_id, parent_id, game_id FROM comments WHERE id = ? AND is_deleted = 0',
            [commentId]
        );

        if (comments.length === 0) {
            return res.status(404).json({ error: '评论不存在' });
        }

        const comment = comments[0];

        if (comment.user_id !== userId) {
            return res.status(403).json({ error: '您只能编辑自己的评论' });
        }

        // 更新评论
        let updateQuery = 'UPDATE comments SET content = ?, edit_count = edit_count + 1, last_edited_at = CURRENT_TIMESTAMP WHERE id = ?';
        let updateParams = [content.trim(), commentId];

        // 如果是顶级评论且提供了评分，则更新评分
        if (comment.parent_id === null && rating !== undefined) {
            if (rating < 1 || rating > 5) {
                return res.status(400).json({ error: '评分必须在1-5之间' });
            }
            updateQuery = 'UPDATE comments SET content = ?, rating = ?, edit_count = edit_count + 1, last_edited_at = CURRENT_TIMESTAMP WHERE id = ?';
            updateParams = [content.trim(), rating, commentId];
        }

        await promisePool.execute(updateQuery, updateParams);

        // 如果更新了评分，重新计算游戏平均评分
        if (comment.parent_id === null && rating !== undefined) {
            await updateGameRating(comment.game_id);
        }

        res.json({ message: '评论更新成功' });

    } catch (error) {
        console.error('编辑评论错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 举报评论
app.post('/api/comments/:id/report', requireAuthWithStatusCheck, async (req, res) => {
    try {
        const commentId = parseInt(req.params.id);
        const { report_type, reason } = req.body;
        const userId = req.session.userId;

        if (isNaN(commentId)) {
            return res.status(400).json({ error: '无效的评论ID' });
        }

        const validReportTypes = ['spam', 'harassment', 'inappropriate', 'fake', 'other'];
        if (!report_type || !validReportTypes.includes(report_type)) {
            return res.status(400).json({ error: '无效的举报类型' });
        }

        // 检查评论是否存在
        const [comments] = await promisePool.execute(
            'SELECT id, user_id FROM comments WHERE id = ? AND is_deleted = 0',
            [commentId]
        );

        if (comments.length === 0) {
            return res.status(404).json({ error: '评论不存在' });
        }

        // 不能举报自己的评论
        if (comments[0].user_id === userId) {
            return res.status(400).json({ error: '不能举报自己的评论' });
        }

        // 检查是否已经举报过
        const [existingReports] = await promisePool.execute(
            'SELECT id FROM comment_reports WHERE comment_id = ? AND reporter_user_id = ?',
            [commentId, userId]
        );

        if (existingReports.length > 0) {
            return res.status(400).json({ error: '您已经举报过这条评论了' });
        }

        // 插入举报记录
        console.log('📝 插入举报记录:', { commentId, userId, report_type, reason: reason || '' });

        const [result] = await promisePool.execute(
            'INSERT INTO comment_reports (comment_id, reporter_user_id, report_type, reason) VALUES (?, ?, ?, ?)',
            [commentId, userId, report_type, reason || '']
        );

        console.log('✅ 举报记录插入成功，ID:', result.insertId);

        res.status(201).json({ message: '举报提交成功，我们会尽快处理' });

    } catch (error) {
        console.error('举报评论错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 游戏搜索API（保持向后兼容）
app.get('/api/games/search', async (req, res) => {
    try {
        const { name } = req.query;

        if (!name) {
            return res.status(400).json({ error: '搜索关键词不能为空' });
        }

        const [games] = await promisePool.execute(
            'SELECT id, name, platform, description, cover_url, avg_rating, review_count FROM games WHERE name LIKE ? ORDER BY avg_rating DESC',
            [`%${name}%`]
        );

        res.json({ games });

    } catch (error) {
        console.error('搜索游戏错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 管理员权限检查中间件
function requireAdmin(req, res, next) {
    if (!req.session.userId) {
        return res.status(401).json({ error: '请先登录' });
    }

    // 检查用户是否为管理员
    promisePool.execute('SELECT role FROM users WHERE id = ?', [req.session.userId])
        .then(([users]) => {
            if (users.length === 0 || users[0].role !== 'admin') {
                return res.status(403).json({ error: '需要管理员权限' });
            }
            next();
        })
        .catch(error => {
            console.error('检查管理员权限错误:', error);
            res.status(500).json({ error: '服务器内部错误' });
        });
}

// 管理员 - 获取所有举报（包括评论和精彩瞬间举报）
app.get('/api/admin/reports', requireAdmin, async (req, res) => {
    try {
        const { status = 'pending', page = 1, limit = 20, type = 'all' } = req.query;

        console.log('收到举报查询请求:', { status, page, limit, type });

        const pageNum = parseInt(page) || 1;
        const limitNum = parseInt(limit) || 20;
        const offset = (pageNum - 1) * limitNum;

        let allReports = [];

        // 获取评论举报
        if (type === 'all' || type === 'comment') {
            try {
                await promisePool.execute('SELECT 1 FROM comment_reports LIMIT 1');

                const [commentReports] = await promisePool.execute(`
                    SELECT
                        cr.id,
                        cr.comment_id as related_id,
                        'comment' as report_category,
                        cr.report_type,
                        cr.reason,
                        cr.status,
                        cr.admin_notes,
                        cr.created_at,
                        cr.updated_at,
                        u_reporter.username as reporter_username,
                        u_reporter.id as reporter_user_id,
                        c.content as comment_content,
                        u_author.username as comment_author,
                        g.name as game_name
                    FROM comment_reports cr
                    JOIN users u_reporter ON cr.reporter_user_id = u_reporter.id
                    JOIN comments c ON cr.comment_id = c.id
                    JOIN users u_author ON c.user_id = u_author.id
                    JOIN games g ON c.game_id = g.id
                    WHERE cr.status = ?
                    ORDER BY cr.created_at DESC
                `, [status]);

                allReports = allReports.concat(commentReports);
            } catch (tableError) {
                console.log('comment_reports表不存在或查询失败');
            }
        }

        // 获取精彩瞬间举报
        if (type === 'all' || type === 'moment') {
            try {
                await promisePool.execute('SELECT 1 FROM moment_reports LIMIT 1');

                const [momentReports] = await promisePool.execute(`
                    SELECT
                        mr.id,
                        mr.moment_id as related_id,
                        'moment' as report_category,
                        mr.report_type,
                        mr.reason,
                        mr.status,
                        mr.admin_notes,
                        mr.created_at,
                        mr.updated_at,
                        u_reporter.username as reporter_username,
                        u_reporter.id as reporter_user_id,
                        gm.title as moment_title,
                        gm.description as moment_description,
                        u_author.username as moment_author,
                        g.name as game_name
                    FROM moment_reports mr
                    JOIN users u_reporter ON mr.reporter_user_id = u_reporter.id
                    JOIN game_moments gm ON mr.moment_id = gm.id
                    JOIN users u_author ON gm.user_id = u_author.id
                    JOIN games g ON gm.game_id = g.id
                    WHERE mr.status = ?
                    ORDER BY mr.created_at DESC
                `, [status]);

                allReports = allReports.concat(momentReports);
            } catch (tableError) {
                console.log('moment_reports表不存在或查询失败');
            }
        }

        // 按创建时间排序
        allReports.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        // 分页
        const total = allReports.length;
        const paginatedReports = allReports.slice(offset, offset + limitNum);

        res.json({
            reports: paginatedReports,
            pagination: {
                page: pageNum,
                limit: limitNum,
                total: total,
                totalPages: Math.ceil(total / limitNum)
            }
        });

    } catch (error) {
        console.error('获取举报列表错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});



// 管理员 - 处理举报（支持评论和精彩瞬间举报）
app.put('/api/admin/reports/:id', requireAdmin, async (req, res) => {
    console.log('🚀 进入处理举报API');
    try {
        const reportId = parseInt(req.params.id);
        const { status, admin_notes, action, report_type } = req.body;

        console.log('🔧 处理举报请求:', {
            reportId,
            status,
            admin_notes,
            action,
            report_type,
            body: req.body
        });

        if (isNaN(reportId)) {
            console.log('❌ 无效的举报ID');
            return res.status(400).json({ error: '无效的举报ID' });
        }

        const validStatuses = ['pending', 'reviewed', 'resolved', 'dismissed'];
        if (!status || !validStatuses.includes(status)) {
            console.log('❌ 无效的状态:', status);
            return res.status(400).json({ error: '无效的状态' });
        }

        console.log('✅ 参数验证通过，开始查找举报记录');

        let reportInfo = null;
        let reporterUserId = null;

        console.log('🔍 查找举报记录，ID:', reportId, '类型:', report_type);

        // 根据前端传递的类型决定查找哪个表
        if (report_type === 'moment') {
            // 查找精彩瞬间举报
            console.log('🔍 查找精彩瞬间举报...');
            const [momentReports] = await promisePool.execute(
                'SELECT moment_id, reporter_user_id FROM moment_reports WHERE id = ?',
                [reportId]
            );
            console.log('📋 精彩瞬间举报查询结果:', momentReports);

            if (momentReports.length > 0) {
                console.log('📝 找到精彩瞬间举报:', momentReports[0]);
                reportInfo = { type: 'moment', related_id: momentReports[0].moment_id };
                reporterUserId = momentReports[0].reporter_user_id;

                console.log('🔄 更新精彩瞬间举报状态...');
                // 更新精彩瞬间举报状态
                const updateResult = await promisePool.execute(
                    'UPDATE moment_reports SET status = ?, admin_notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [status, admin_notes || '', reportId]
                );
                console.log('✅ 精彩瞬间举报更新结果:', updateResult);

                // 验证更新是否成功
                const [verifyResult] = await promisePool.execute(
                    'SELECT status, admin_notes FROM moment_reports WHERE id = ?',
                    [reportId]
                );
                console.log('🔍 验证更新后的状态:', verifyResult);

                // 如果需要删除精彩瞬间
                if (action === 'delete_moment') {
                    console.log('🗑️ 删除精彩瞬间:', momentReports[0].moment_id);
                    await promisePool.execute(
                        'UPDATE game_moments SET is_deleted = 1 WHERE id = ?',
                        [momentReports[0].moment_id]
                    );
                }
            } else {
                console.log('❌ 精彩瞬间举报不存在，reportId:', reportId);
                return res.status(404).json({ error: '精彩瞬间举报不存在' });
            }
        } else {
            // 查找评论举报（默认或明确指定为comment）
            console.log('🔍 查找评论举报...');
            const [commentReports] = await promisePool.execute(
                'SELECT comment_id, reporter_user_id FROM comment_reports WHERE id = ?',
                [reportId]
            );
            console.log('📋 评论举报查询结果:', commentReports);

            if (commentReports.length > 0) {
                console.log('📝 找到评论举报:', commentReports[0]);
                reportInfo = { type: 'comment', related_id: commentReports[0].comment_id };
                reporterUserId = commentReports[0].reporter_user_id;

                console.log('🔄 更新评论举报状态...');
                // 更新评论举报状态
                const updateResult = await promisePool.execute(
                    'UPDATE comment_reports SET status = ?, admin_notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [status, admin_notes || '', reportId]
                );
                console.log('✅ 评论举报更新结果:', updateResult);

                // 如果需要删除评论
                if (action === 'delete_comment') {
                    console.log('🗑️ 删除评论:', commentReports[0].comment_id);
                    await promisePool.execute(
                        'UPDATE comments SET is_deleted = 1 WHERE id = ?',
                        [commentReports[0].comment_id]
                    );
                }
            } else {
                console.log('❌ 评论举报不存在，reportId:', reportId);
                return res.status(404).json({ error: '评论举报不存在' });
            }
        }

        // 创建举报处理结果通知
        if (reporterUserId) {
            const statusText = {
                'reviewed': '已审核',
                'resolved': '已解决',
                'dismissed': '已驳回'
            }[status] || status;

            try {
                await createNotification(
                    reporterUserId,
                    'report_result',
                    `您的举报已${statusText}`,
                    `举报处理结果：${statusText}${admin_notes ? `。管理员备注：${admin_notes}` : ''}`,
                    reportId,
                    'report',
                    req.session.userId // 管理员ID作为发送者
                );
                console.log('✅ 举报处理通知已创建');
            } catch (notificationError) {
                console.error('❌ 创建举报处理通知失败:', notificationError);
                // 不要因为通知失败而影响举报处理
            }
        }

        console.log('🎉 举报处理完成，准备返回响应');
        res.json({ message: '举报处理成功' });

    } catch (error) {
        console.error('❌ 处理举报发生异常:', error);
        console.error('❌ 异常堆栈:', error.stack);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 管理员 - 获取用户列表
app.get('/api/admin/users', requireAdmin, async (req, res) => {
    try {
        const { page = 1, limit = 20, search, status } = req.query;

        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const offset = (pageNum - 1) * limitNum;

        // 移除未使用的变量，直接在下面构建查询

        // 使用字符串拼接避免参数绑定问题
        const limitParam = parseInt(limitNum);
        const offsetParam = parseInt(offset);

        // 构建安全的WHERE子句
        let safeWhereClause = '';
        if (search) {
            const searchParam = promisePool.escape(`%${search}%`);
            safeWhereClause += ` WHERE (username LIKE ${searchParam} OR email LIKE ${searchParam})`;
        }
        if (status) {
            const statusParam = promisePool.escape(status);
            safeWhereClause += safeWhereClause ? ` AND status = ${statusParam}` : ` WHERE status = ${statusParam}`;
        }

        console.log('用户查询参数:', { safeWhereClause, limitParam, offsetParam });

        // 查询用户信息，包括状态字段
        const [users] = await promisePool.execute(
            `SELECT id, username, email, role, status, created_at, last_login_at
             FROM users ${safeWhereClause}
             ORDER BY created_at DESC
             LIMIT ${limitParam} OFFSET ${offsetParam}`
        );

        const [countResult] = await promisePool.execute(
            `SELECT COUNT(*) as total FROM users ${safeWhereClause}`
        );

        res.json({
            users,
            pagination: {
                page: pageNum,
                limit: limitNum,
                total: countResult[0].total,
                totalPages: Math.ceil(countResult[0].total / limitNum)
            }
        });

    } catch (error) {
        console.error('获取用户列表错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 备用API - 简化的举报列表
app.get('/api/admin/reports/simple', requireAdmin, async (req, res) => {
    try {
        console.log('使用简化举报API');
        const [reports] = await promisePool.execute(
            'SELECT * FROM comment_reports ORDER BY created_at DESC LIMIT 10'
        );
        res.json({ reports, total: reports.length });
    } catch (error) {
        console.error('简化举报API错误:', error);
        res.json({ reports: [], total: 0 });
    }
});

// 备用API - 简化的用户列表
app.get('/api/admin/users/simple', requireAdmin, async (req, res) => {
    try {
        console.log('使用简化用户API');
        const [users] = await promisePool.execute(
            'SELECT id, username, email, role, status, created_at, last_login_at FROM users ORDER BY created_at DESC LIMIT 20'
        );
        res.json({ users, total: users.length });
    } catch (error) {
        console.error('简化用户API错误:', error);
        res.json({ users: [], total: 0 });
    }
});

// 管理员 - 更新用户状态
app.put('/api/admin/users/:id/status', requireAdmin, async (req, res) => {
    try {
        const userId = parseInt(req.params.id);
        const { status } = req.body;

        if (isNaN(userId)) {
            return res.status(400).json({ error: '无效的用户ID' });
        }

        const validStatuses = ['active', 'suspended', 'banned'];
        if (!status || !validStatuses.includes(status)) {
            return res.status(400).json({ error: '无效的状态' });
        }

        // 不能修改自己的状态
        if (userId === req.session.userId) {
            return res.status(400).json({ error: '不能修改自己的状态' });
        }

        await promisePool.execute(
            'UPDATE users SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [status, userId]
        );

        res.json({ message: '用户状态更新成功' });

    } catch (error) {
        console.error('更新用户状态错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 管理员 - 添加新游戏
app.post('/api/admin/games', requireAdmin, async (req, res) => {
    try {
        console.log('🎮 收到添加游戏请求:', req.body);

        const {
            name,
            platform,
            description,
            cover_url,
            genre,
            developer,
            publisher,
            release_date,
            price,
            is_featured
        } = req.body;

        // 验证必填字段
        if (!name || !platform) {
            return res.status(400).json({ error: '游戏名称和平台是必填项' });
        }

        if (name.length > 200) {
            return res.status(400).json({ error: '游戏名称不能超过200字符' });
        }

        if (platform.length > 100) {
            return res.status(400).json({ error: '平台信息不能超过100字符' });
        }

        // 生成搜索关键词
        const search_keywords = [name, genre, developer, publisher].filter(Boolean).join(' ');

        console.log('📝 准备插入游戏数据:', {
            name, platform, description, cover_url, genre,
            developer, publisher, release_date, price, is_featured
        });

        // 插入新游戏
        const [result] = await promisePool.execute(
            `INSERT INTO games (
                name, platform, description, cover_url, genre, developer,
                publisher, release_date, price, is_featured, search_keywords
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                name,
                platform,
                description || null,
                cover_url || null,
                genre || null,
                developer || null,
                publisher || null,
                release_date || null,
                price || null,
                is_featured || false,
                search_keywords
            ]
        );

        console.log('✅ 游戏插入成功，ID:', result.insertId);

        res.status(201).json({
            message: '游戏添加成功',
            gameId: result.insertId
        });

    } catch (error) {
        console.error('添加游戏错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 前端路由 - 所有非API请求都返回index.html
app.get('*', (req, res, next) => {
    if (!req.path.startsWith('/api')) {
        res.sendFile(path.join(__dirname, '../frontend/index.html'));
    } else {
        // 如果是API请求，继续到下一个路由
        next();
    }
});

// 管理员 - 更新游戏信息
app.put('/api/admin/games/:id', requireAdmin, async (req, res) => {
    try {
        const gameId = parseInt(req.params.id);
        const {
            name,
            platform,
            description,
            cover_url,
            genre,
            developer,
            publisher,
            release_date,
            price,
            is_featured
        } = req.body;

        if (isNaN(gameId)) {
            return res.status(400).json({ error: '无效的游戏ID' });
        }

        // 验证必填字段
        if (!name || !platform) {
            return res.status(400).json({ error: '游戏名称和平台是必填项' });
        }

        if (name.length > 200) {
            return res.status(400).json({ error: '游戏名称不能超过200字符' });
        }

        if (platform.length > 100) {
            return res.status(400).json({ error: '平台信息不能超过100字符' });
        }

        // 检查游戏是否存在
        const [existingGames] = await promisePool.execute(
            'SELECT id FROM games WHERE id = ?',
            [gameId]
        );

        if (existingGames.length === 0) {
            return res.status(404).json({ error: '游戏不存在' });
        }

        // 生成搜索关键词
        const search_keywords = [name, genre, developer, publisher].filter(Boolean).join(' ');

        // 更新游戏信息
        await promisePool.execute(
            `UPDATE games SET
                name = ?, platform = ?, description = ?, cover_url = ?,
                genre = ?, developer = ?, publisher = ?, release_date = ?,
                price = ?, is_featured = ?, search_keywords = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?`,
            [
                name,
                platform,
                description || null,
                cover_url || null,
                genre || null,
                developer || null,
                publisher || null,
                release_date || null,
                price || null,
                is_featured || false,
                search_keywords,
                gameId
            ]
        );

        res.json({ message: '游戏信息更新成功' });

    } catch (error) {
        console.error('更新游戏信息错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 管理员 - 删除游戏
app.delete('/api/admin/games/:id', requireAdmin, async (req, res) => {
    try {
        const gameId = parseInt(req.params.id);

        if (isNaN(gameId)) {
            return res.status(400).json({ error: '无效的游戏ID' });
        }

        // 检查游戏是否存在
        const [existingGames] = await promisePool.execute(
            'SELECT id FROM games WHERE id = ?',
            [gameId]
        );

        if (existingGames.length === 0) {
            return res.status(404).json({ error: '游戏不存在' });
        }

        // 删除游戏（这会级联删除相关的评论）
        await promisePool.execute('DELETE FROM games WHERE id = ?', [gameId]);

        res.json({ message: '游戏删除成功' });

    } catch (error) {
        console.error('删除游戏错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 临时API：重新计算所有游戏的评分统计
app.post('/api/admin/recalculate-ratings', requireAdmin, async (req, res) => {
    try {
        console.log('🔄 开始重新计算所有游戏的评分统计...');
        const [games] = await promisePool.execute('SELECT id, name FROM games');

        for (const game of games) {
            await updateGameRating(game.id);
        }

        res.json({ message: '所有游戏评分统计已重新计算' });
    } catch (error) {
        console.error('重新计算评分错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// ==================== 通知系统 API ====================

// 超级简单的测试API
app.get('/api/test-simple', (req, res) => {
    console.log('🎯 收到超级简单测试API请求');
    console.log('🔍 请求详情:', {
        method: req.method,
        url: req.url,
        headers: req.headers,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });
    res.json({ message: '测试成功', timestamp: new Date().toISOString() });
});

// 终极字符集修复API
app.post('/api/ultimate-charset-fix', async (req, res) => {
    console.log('📥 收到终极字符集修复请求');

    try {
        // 1. 检查并修复所有MySQL字符集变量
        console.log('🔧 步骤1: 修复MySQL字符集变量...');
        const charsetCommands = [
            'SET character_set_client = utf8mb4',
            'SET character_set_connection = utf8mb4',
            'SET character_set_database = utf8mb4',
            'SET character_set_results = utf8mb4',
            'SET character_set_server = utf8mb4',
            'SET collation_connection = utf8mb4_unicode_ci',
            'SET collation_database = utf8mb4_unicode_ci',
            'SET collation_server = utf8mb4_unicode_ci',
            'SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci'
        ];

        for (const cmd of charsetCommands) {
            try {
                await promisePool.query(cmd);
                console.log('✅', cmd);
            } catch (e) {
                console.log('⚠️', cmd, '失败:', e.message);
            }
        }

        // 2. 重新创建comments表（备份数据）
        console.log('🔧 步骤2: 备份并重建comments表...');

        // 备份现有数据
        const [existingComments] = await promisePool.query('SELECT * FROM comments');
        console.log('📋 备份了', existingComments.length, '条评论数据');

        // 删除外键约束
        try {
            await promisePool.query('ALTER TABLE comments DROP FOREIGN KEY comments_ibfk_1');
        } catch (e) { console.log('删除外键1失败:', e.message); }

        try {
            await promisePool.query('ALTER TABLE comments DROP FOREIGN KEY comments_ibfk_2');
        } catch (e) { console.log('删除外键2失败:', e.message); }

        try {
            await promisePool.query('ALTER TABLE comments DROP FOREIGN KEY comments_ibfk_3');
        } catch (e) { console.log('删除外键3失败:', e.message); }

        // 重新创建表
        await promisePool.query('DROP TABLE IF EXISTS comments_backup');
        await promisePool.query('CREATE TABLE comments_backup AS SELECT * FROM comments');
        await promisePool.query('DROP TABLE comments');

        await promisePool.query(`
            CREATE TABLE comments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                game_id INT NOT NULL,
                rating DECIMAL(2,1) NULL COMMENT '评分，1-5分，回复评论时为NULL',
                content TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
                parent_id INT DEFAULT NULL COMMENT '父评论ID，NULL表示顶级评论',
                is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否已删除',
                likes_count INT DEFAULT 0 COMMENT '点赞数',
                dislikes_count INT DEFAULT 0 COMMENT '点踩数',
                edit_count INT DEFAULT 0,
                last_edited_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_game_id (game_id),
                INDEX idx_user_id (user_id),
                INDEX idx_parent_id (parent_id),
                INDEX idx_created_at (created_at DESC)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论表'
        `);

        // 恢复数据
        if (existingComments.length > 0) {
            console.log('🔧 步骤3: 恢复数据...');
            for (const comment of existingComments) {
                try {
                    await promisePool.query(`
                        INSERT INTO comments (id, user_id, game_id, rating, content, parent_id, is_deleted, likes_count, dislikes_count, edit_count, last_edited_at, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        comment.id, comment.user_id, comment.game_id, comment.rating,
                        comment.content, comment.parent_id, comment.is_deleted,
                        comment.likes_count, comment.dislikes_count, comment.edit_count,
                        comment.last_edited_at, comment.created_at, comment.updated_at
                    ]);
                } catch (e) {
                    console.log('恢复评论失败:', comment.id, e.message);
                }
            }
        }

        res.json({
            success: true,
            message: '终极字符集修复完成',
            backupCount: existingComments.length
        });

    } catch (error) {
        console.error('❌ 终极字符集修复错误:', error);
        res.status(500).json({
            success: false,
            error: '终极字符集修复失败: ' + error.message
        });
    }
});

// 触发器管理API
app.post('/api/manage-triggers', async (req, res) => {
    console.log('📥 收到触发器管理请求');

    try {
        const { action } = req.body; // 'disable' 或 'enable'

        if (action === 'disable') {
            console.log('🔧 禁用评论相关触发器...');

            // 禁用触发器 - 使用query而不是execute
            await promisePool.query('DROP TRIGGER IF EXISTS update_comment_likes_after_vote_insert');
            console.log('✅ 触发器1已删除');

            await promisePool.query('DROP TRIGGER IF EXISTS update_comment_likes_after_vote_delete');
            console.log('✅ 触发器2已删除');

            await promisePool.query('DROP TRIGGER IF EXISTS update_comment_likes_after_vote_update');
            console.log('✅ 触发器3已删除');

            console.log('✅ 所有触发器已禁用');

        } else if (action === 'enable') {
            console.log('🔧 重新创建评论相关触发器...');

            // 重新创建触发器 - 使用query而不是execute
            await promisePool.query(`
                CREATE TRIGGER update_comment_likes_after_vote_insert
                AFTER INSERT ON comment_votes
                FOR EACH ROW
                BEGIN
                    IF NEW.vote_type = 'like' THEN
                        UPDATE comments SET likes_count = likes_count + 1 WHERE id = NEW.comment_id;
                    ELSEIF NEW.vote_type = 'dislike' THEN
                        UPDATE comments SET dislikes_count = dislikes_count + 1 WHERE id = NEW.comment_id;
                    END IF;
                END
            `);
            console.log('✅ 触发器1已重新创建');

            await promisePool.query(`
                CREATE TRIGGER update_comment_likes_after_vote_delete
                AFTER DELETE ON comment_votes
                FOR EACH ROW
                BEGIN
                    IF OLD.vote_type = 'like' THEN
                        UPDATE comments SET likes_count = likes_count - 1 WHERE id = OLD.comment_id;
                    ELSEIF OLD.vote_type = 'dislike' THEN
                        UPDATE comments SET dislikes_count = dislikes_count - 1 WHERE id = OLD.comment_id;
                    END IF;
                END
            `);
            console.log('✅ 触发器2已重新创建');

            await promisePool.query(`
                CREATE TRIGGER update_comment_likes_after_vote_update
                AFTER UPDATE ON comment_votes
                FOR EACH ROW
                BEGIN
                    IF OLD.vote_type = 'like' THEN
                        UPDATE comments SET likes_count = likes_count - 1 WHERE id = OLD.comment_id;
                    ELSEIF OLD.vote_type = 'dislike' THEN
                        UPDATE comments SET dislikes_count = dislikes_count - 1 WHERE id = OLD.comment_id;
                    END IF;

                    IF NEW.vote_type = 'like' THEN
                        UPDATE comments SET likes_count = likes_count + 1 WHERE id = NEW.comment_id;
                    ELSEIF NEW.vote_type = 'dislike' THEN
                        UPDATE comments SET dislikes_count = dislikes_count + 1 WHERE id = NEW.comment_id;
                    END IF;
                END
            `);
            console.log('✅ 触发器3已重新创建');

            console.log('✅ 所有触发器已重新创建');
        }

        res.json({
            success: true,
            message: `触发器${action === 'disable' ? '禁用' : '启用'}成功`
        });

    } catch (error) {
        console.error('❌ 触发器管理错误:', error);
        res.status(500).json({
            success: false,
            error: '触发器管理失败: ' + error.message
        });
    }
});

// 字符集修复API
app.post('/api/fix-charset', async (req, res) => {
    console.log('📥 收到字符集修复请求');

    try {
        // 强制修复comment_votes表的字符集
        console.log('🔧 修复comment_votes表字符集...');
        await promisePool.execute(`
            ALTER TABLE comment_votes CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
        `);
        console.log('✅ comment_votes表字符集修复成功');

        // 强制设置会话字符集
        console.log('🔧 设置会话字符集...');
        const sessionQueries = [
            'SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci',
            'SET character_set_client = utf8mb4',
            'SET character_set_connection = utf8mb4',
            'SET character_set_results = utf8mb4',
            'SET collation_connection = utf8mb4_unicode_ci'
        ];

        for (const query of sessionQueries) {
            await promisePool.execute(query);
            console.log('✅ 执行成功:', query);
        }

        res.json({
            success: true,
            message: '字符集修复成功'
        });

    } catch (error) {
        console.error('❌ 字符集修复错误:', error);
        res.status(500).json({
            success: false,
            error: '字符集修复失败: ' + error.message
        });
    }
});

// 深度诊断API - 检查触发器和约束
app.get('/api/db-deep-diagnosis', async (req, res) => {
    console.log('📥 收到深度诊断请求');

    try {
        // 检查MySQL服务器字符集设置
        const [serverCharset] = await promisePool.execute(`
            SHOW VARIABLES LIKE 'character_set%'
        `);
        console.log('📋 服务器字符集设置:', serverCharset);

        // 检查排序规则设置
        const [collationVars] = await promisePool.execute(`
            SHOW VARIABLES LIKE 'collation%'
        `);
        console.log('📋 排序规则设置:', collationVars);

        // 检查comments表的触发器
        const [triggers] = await promisePool.execute(`
            SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE
            FROM information_schema.TRIGGERS
            WHERE EVENT_OBJECT_SCHEMA = 'game_review_platform'
            AND EVENT_OBJECT_TABLE = 'comments'
        `);
        console.log('📋 comments表触发器:', triggers);

        // 检查外键约束
        const [foreignKeys] = await promisePool.execute(`
            SELECT CONSTRAINT_NAME, TABLE_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = 'game_review_platform'
            AND TABLE_NAME = 'comments'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        `);
        console.log('📋 comments表外键:', foreignKeys);

        res.json({
            success: true,
            message: '深度诊断成功',
            serverCharset: serverCharset,
            collationVars: collationVars,
            triggers: triggers,
            foreignKeys: foreignKeys
        });

    } catch (error) {
        console.error('❌ 深度诊断错误:', error);
        res.status(500).json({
            success: false,
            error: '深度诊断失败: ' + error.message
        });
    }
});

// 全面的数据库字符集检查API
app.get('/api/db-charset-full-test', async (req, res) => {
    console.log('📥 收到全面数据库字符集检查请求');

    try {
        // 检查所有相关表的字符集
        const [allTables] = await promisePool.execute(`
            SELECT TABLE_NAME, TABLE_COLLATION
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = 'game_review_platform'
            ORDER BY TABLE_NAME
        `);
        console.log('📋 所有表字符集:', allTables);

        // 检查所有字符字段的字符集
        const [allColumns] = await promisePool.execute(`
            SELECT TABLE_NAME, COLUMN_NAME, CHARACTER_SET_NAME, COLLATION_NAME
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = 'game_review_platform'
            AND CHARACTER_SET_NAME IS NOT NULL
            ORDER BY TABLE_NAME, COLUMN_NAME
        `);
        console.log('📋 所有字符字段字符集:', allColumns);

        // 检查是否有gbk字符集的字段
        const gbkFields = allColumns.filter(col =>
            col.CHARACTER_SET_NAME?.includes('gbk') ||
            col.COLLATION_NAME?.includes('gbk')
        );
        console.log('🚨 GBK字符集字段:', gbkFields);

        // 检查字符集不一致的字段
        const inconsistentFields = allColumns.filter(col =>
            col.COLLATION_NAME !== 'utf8mb4_unicode_ci'
        );
        console.log('⚠️ 字符集不一致的字段:', inconsistentFields);

        res.json({
            success: true,
            message: '全面字符集检查成功',
            allTables: allTables,
            allColumns: allColumns,
            gbkFields: gbkFields,
            hasGbkFields: gbkFields.length > 0,
            inconsistentFields: inconsistentFields,
            hasInconsistentFields: inconsistentFields.length > 0
        });

    } catch (error) {
        console.error('❌ 全面字符集检查错误:', error);
        res.status(500).json({
            success: false,
            error: '全面字符集检查失败: ' + error.message
        });
    }
});

// 数据库字符集检查API
app.get('/api/db-charset-test', async (req, res) => {
    console.log('📥 收到数据库字符集检查请求');

    try {
        // 检查数据库字符集
        console.log('🔍 检查数据库字符集...');
        const [dbCharset] = await promisePool.execute('SELECT @@character_set_database, @@collation_database');
        console.log('📋 数据库字符集:', dbCharset);

        // 检查连接字符集
        const [connCharset] = await promisePool.execute('SELECT @@character_set_connection, @@collation_connection');
        console.log('📋 连接字符集:', connCharset);

        // 检查comments表字符集
        const [tableCharset] = await promisePool.execute(`
            SELECT TABLE_SCHEMA, TABLE_NAME, TABLE_COLLATION
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = 'game_review_platform' AND TABLE_NAME = 'comments'
        `);
        console.log('📋 comments表字符集:', tableCharset);

        // 检查comments表字段字符集
        const [columnCharset] = await promisePool.execute(`
            SELECT COLUMN_NAME, CHARACTER_SET_NAME, COLLATION_NAME
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = 'game_review_platform' AND TABLE_NAME = 'comments'
            AND CHARACTER_SET_NAME IS NOT NULL
        `);
        console.log('📋 comments表字段字符集:', columnCharset);

        res.json({
            success: true,
            message: '字符集检查成功',
            database: dbCharset[0],
            connection: connCharset[0],
            table: tableCharset[0],
            columns: columnCharset
        });

    } catch (error) {
        console.error('❌ 字符集检查错误:', error);
        res.status(500).json({
            success: false,
            error: '字符集检查失败: ' + error.message
        });
    }
});

// 数据库测试API - 检查通知表
app.get('/api/notifications-db-test', async (req, res) => {
    console.log('📥 收到数据库测试API请求');

    try {
        // 检查通知表是否存在
        console.log('🔍 检查通知表...');
        const [tables] = await promisePool.execute('SHOW TABLES LIKE "notifications"');
        console.log('📋 通知表检查结果:', tables);

        if (tables.length === 0) {
            return res.json({
                success: false,
                message: '通知表不存在',
                tables: tables
            });
        }

        // 检查表结构
        console.log('🔍 检查表结构...');
        const [columns] = await promisePool.execute('DESCRIBE notifications');
        console.log('📋 表结构:', columns);

        // 查询所有通知数据
        console.log('🔍 查询所有通知数据...');
        const [allNotifications] = await promisePool.execute('SELECT * FROM notifications LIMIT 10');
        console.log('📋 通知数据:', allNotifications);

        res.json({
            success: true,
            message: '数据库测试成功',
            tableExists: tables.length > 0,
            columns: columns,
            sampleData: allNotifications
        });

    } catch (error) {
        console.error('❌ 数据库测试API错误:', error);
        res.status(500).json({
            success: false,
            error: '数据库测试失败: ' + error.message
        });
    }
});

// 临时测试API - 不需要认证
app.get('/api/notifications-test', async (req, res) => {
    console.log('📥 收到测试通知API请求');

    try {
        // 直接返回模拟数据，不查询数据库
        const mockNotifications = [
            {
                id: 1,
                type: 'vote',
                title: '您的评论收到了点赞',
                content: '用户 testuser2 点赞了您的评论',
                is_read: false,
                created_at: new Date().toISOString()
            },
            {
                id: 2,
                type: 'reply',
                title: '您的评论收到了回复',
                content: '用户 testuser3 回复了您的评论',
                is_read: true,
                created_at: new Date().toISOString()
            }
        ];

        console.log('📋 返回模拟通知数据，数量:', mockNotifications.length);

        const responseData = {
            notifications: mockNotifications,
            unread_count: mockNotifications.filter(n => !n.is_read).length,
            page: 1,
            limit: 10
        };

        console.log('📤 发送测试通知API响应:', responseData);
        res.json(responseData);

    } catch (error) {
        console.error('获取测试通知列表失败:', error);
        res.status(500).json({ error: '获取测试通知列表失败' });
    }
});





// 通知API - 从数据库获取真实数据
app.get('/api/notifications', async (req, res) => {
    console.log('🎯 通知API开始执行');
    console.log('📥 收到通知API请求:', {
        query: req.query,
        url: req.url,
        method: req.method,
        hasSession: !!req.session,
        userId: req.session?.userId
    });

    try {
        // 检查用户是否登录
        if (!req.session || !req.session.userId) {
            console.log('❌ 用户未登录，返回401');
            return res.status(401).json({ error: '请先登录' });
        }

        console.log('✅ 用户已登录，用户ID:', req.session.userId);

        const { page = 1, limit = 20, unread_only = false, type = null } = req.query;
        const offset = (page - 1) * limit;

        console.log('📊 查询参数:', { page, limit, unread_only, type, offset });

        let whereClause = 'WHERE n.user_id = ?';
        let params = [req.session.userId];

        if (unread_only === 'true') {
            whereClause += ' AND n.is_read = FALSE';
        }

        // 添加类型筛选
        if (type && type !== 'all') {
            if (type === 'like') {
                // 点赞包括 like 和 dislike 类型
                whereClause += ' AND n.type IN (?, ?)';
                params.push('like', 'dislike');
            } else {
                whereClause += ' AND n.type = ?';
                params.push(type);
            }
        }

        // 首先检查通知表是否存在
        try {
            console.log('🔍 检查通知表是否存在...');
            const [tableCheck] = await promisePool.execute('SHOW TABLES LIKE "notifications"');
            console.log('📋 通知表检查结果:', tableCheck);

            if (tableCheck.length === 0) {
                console.log('❌ 通知表不存在，返回空数据');
                return res.json({
                    notifications: [],
                    unread_count: 0,
                    page: parseInt(page),
                    limit: parseInt(limit),
                    message: '通知表不存在，请先创建数据库表'
                });
            }
        } catch (tableError) {
            console.error('❌ 检查通知表失败:', tableError);
            return res.status(500).json({ error: '数据库表检查失败: ' + tableError.message });
        }

        // 先尝试简单查询，不使用LIMIT
        console.log('🔍 尝试简单查询（不使用LIMIT）...');

        try {
            const [simpleTest] = await promisePool.execute(`
                SELECT COUNT(*) as total FROM notifications WHERE user_id = ?
            `, [req.session.userId]);

            console.log('✅ 简单查询成功，用户通知总数:', simpleTest[0].total);
        } catch (simpleError) {
            console.error('❌ 简单查询也失败:', simpleError);
            throw simpleError;
        }

        // 使用字符串拼接而不是参数化查询来避免LIMIT问题
        const limitClause = `LIMIT ${parseInt(offset)}, ${parseInt(limit)}`;

        console.log('🔍 执行通知查询:', {
            whereClause,
            params,
            limitClause,
            userId: req.session.userId
        });

        const [notifications] = await promisePool.execute(`
            SELECT
                n.id,
                n.type,
                n.title,
                n.content,
                n.related_id,
                n.related_type,
                n.is_read,
                n.created_at,
                u.username as sender_username,
                u.id as sender_id
            FROM notifications n
            LEFT JOIN users u ON n.sender_id = u.id
            ${whereClause}
            ORDER BY n.created_at DESC
            ${limitClause}
        `, params);

        console.log('📋 查询到的通知数量:', notifications.length);

        // 获取未读通知数量
        const [unreadCount] = await promisePool.execute(
            'SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = FALSE',
            [req.session.userId]
        );

        const responseData = {
            notifications,
            unread_count: unreadCount[0].count,
            page: parseInt(page),
            limit: parseInt(limit)
        };

        console.log('📤 发送通知API响应:', {
            notificationCount: notifications.length,
            unreadCount: unreadCount[0].count,
            page: parseInt(page),
            limit: parseInt(limit)
        });

        res.json(responseData);

    } catch (error) {
        console.error('❌ 获取通知列表失败:', error);
        res.status(500).json({ error: '获取通知列表失败' });
    }
});

// 原始的通知API（备用）
app.get('/api/notifications-original', requireAuth, async (req, res) => {
    console.log('🎯 原始通知API处理函数开始执行');
    console.log('📥 收到通知API请求:', {
        userId: req.session.userId,
        query: req.query,
        url: req.url,
        method: req.method
    });

    try {
        const { page = 1, limit = 20, unread_only = false } = req.query;
        const offset = (page - 1) * limit;

        console.log('📊 通知查询参数:', { page, limit, unread_only, offset });

        let whereClause = 'WHERE n.user_id = ?';
        let params = [req.session.userId];

        if (unread_only === 'true') {
            whereClause += ' AND n.is_read = FALSE';
        }

        // 获取通知列表，包含发送者信息
        console.log('🔍 执行通知查询:', { whereClause, params: [...params, parseInt(limit), parseInt(offset)] });

        const [notifications] = await promisePool.execute(`
            SELECT
                n.id,
                n.type,
                n.title,
                n.content,
                n.related_id,
                n.related_type,
                n.is_read,
                n.created_at,
                u.username as sender_username,
                u.id as sender_id
            FROM notifications n
            LEFT JOIN users u ON n.sender_id = u.id
            ${whereClause}
            ORDER BY n.created_at DESC
            LIMIT ? OFFSET ?
        `, [...params, parseInt(limit), parseInt(offset)]);

        console.log('📋 查询到的通知数量:', notifications.length);

        // 获取未读通知数量
        const [unreadCount] = await promisePool.execute(
            'SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = FALSE',
            [req.session.userId]
        );

        const responseData = {
            notifications,
            unread_count: unreadCount[0].count,
            page: parseInt(page),
            limit: parseInt(limit)
        };

        console.log('📤 发送通知API响应:', {
            notificationCount: notifications.length,
            unreadCount: unreadCount[0].count,
            page: parseInt(page),
            limit: parseInt(limit)
        });

        res.json(responseData);

    } catch (error) {
        console.error('获取通知列表失败:', error);
        res.status(500).json({ error: '获取通知列表失败' });
    }
});

// 标记通知为已读
app.put('/api/notifications/:id/read', requireAuth, async (req, res) => {
    try {
        const notificationId = req.params.id;

        // 验证通知是否属于当前用户
        const [notifications] = await promisePool.execute(
            'SELECT id FROM notifications WHERE id = ? AND user_id = ?',
            [notificationId, req.session.userId]
        );

        if (notifications.length === 0) {
            return res.status(404).json({ error: '通知不存在' });
        }

        // 标记为已读
        await promisePool.execute(
            'UPDATE notifications SET is_read = TRUE, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [notificationId]
        );

        res.json({ message: '通知已标记为已读' });

    } catch (error) {
        console.error('标记通知已读失败:', error);
        res.status(500).json({ error: '标记通知已读失败' });
    }
});

// 批量标记通知为已读
app.put('/api/notifications/read-all', requireAuth, async (req, res) => {
    try {
        await promisePool.execute(
            'UPDATE notifications SET is_read = TRUE, updated_at = CURRENT_TIMESTAMP WHERE user_id = ? AND is_read = FALSE',
            [req.session.userId]
        );

        res.json({ message: '所有通知已标记为已读' });

    } catch (error) {
        console.error('批量标记通知已读失败:', error);
        res.status(500).json({ error: '批量标记通知已读失败' });
    }
});

// ==================== 精彩瞬间API ====================

// 发布精彩瞬间
app.post('/api/moments', requireAuthWithStatusCheck, upload.single('media'), async (req, res) => {
    try {
        const { title, description, gameId } = req.body;
        const userId = req.session.userId;

        if (!title || !gameId || !req.file) {
            return res.status(400).json({ error: '标题、游戏ID和媒体文件都是必填项' });
        }

        // 检查游戏是否存在
        const [games] = await promisePool.execute('SELECT id FROM games WHERE id = ?', [gameId]);
        if (games.length === 0) {
            return res.status(404).json({ error: '游戏不存在' });
        }

        const mediaType = req.file.mimetype.startsWith('image/') ? 'image' : 'video';
        const mediaUrl = `/uploads/moments/${req.file.filename}`;

        // 插入瞬间记录
        const [result] = await promisePool.execute(`
            INSERT INTO game_moments (user_id, game_id, title, description, media_type, media_url, file_size)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [userId, gameId, title, description || null, mediaType, mediaUrl, req.file.size]);

        res.status(201).json({
            message: '精彩瞬间发布成功',
            momentId: result.insertId
        });

    } catch (error) {
        console.error('发布精彩瞬间失败:', error);
        res.status(500).json({ error: '发布失败' });
    }
});

// 获取游戏的精彩瞬间列表
app.get('/api/games/:gameId/moments', async (req, res) => {
    try {
        const gameId = parseInt(req.params.gameId);
        const { page = 1, limit = 20 } = req.query;
        const offset = (page - 1) * parseInt(limit);

        console.log('🎮 获取精彩瞬间请求:', {
            gameId,
            page,
            limit,
            offset,
            userId: req.session?.userId
        });

        if (isNaN(gameId)) {
            return res.status(400).json({ error: '无效的游戏ID' });
        }

        // 首先检查表结构
        try {
            console.log('🔍 检查表结构...');
            const [tableInfo] = await promisePool.execute('DESCRIBE game_moments');
            console.log('📊 表结构:', tableInfo.map(col => `${col.Field}(${col.Type})`));

            // 检查是否有数据
            const [countCheck] = await promisePool.execute('SELECT COUNT(*) as count FROM game_moments');
            console.log('📊 表中数据数量:', countCheck[0].count);

        } catch (tableError) {
            console.error('❌ 表检查失败:', tableError);
            return res.json({
                moments: [],
                total: 0,
                page: parseInt(page),
                limit: parseInt(limit),
                message: '精彩瞬间功能正在初始化中...'
            });
        }

        // 尝试最基础的查询
        console.log('📊 尝试基础查询...');
        console.log('参数:', { gameId, limit: parseInt(limit), offset });

        try {
            // 尝试最简单的查询，不使用LIMIT和OFFSET
            console.log('📊 尝试无分页查询...');
            const [allMoments] = await promisePool.execute(
                'SELECT * FROM game_moments WHERE game_id = ? AND is_deleted = FALSE ORDER BY created_at DESC',
                [gameId]
            );
            console.log('✅ 无分页查询成功，总结果数量:', allMoments.length);

            // 手动实现分页
            const startIndex = offset;
            const endIndex = startIndex + parseInt(limit);
            const paginatedMoments = allMoments.slice(startIndex, endIndex);
            console.log('📊 分页后结果数量:', paginatedMoments.length);

            // 如果需要用户名和头像，再查询用户信息
            const moments = [];
            for (const moment of paginatedMoments) {
                try {
                    const [users] = await promisePool.execute(
                        `SELECT u.username, p.avatar_url
                         FROM users u
                         LEFT JOIN user_profiles p ON u.id = p.user_id
                         WHERE u.id = ?`,
                        [moment.user_id]
                    );

                    if (users.length > 0) {
                        moments.push({
                            ...moment,
                            username: users[0].username,
                            avatar_url: users[0].avatar_url
                        });
                    } else {
                        moments.push({
                            ...moment,
                            username: '未知用户',
                            avatar_url: null
                        });
                    }
                } catch (userError) {
                    console.error('❌ 查询用户失败:', userError);
                    moments.push({
                        ...moment,
                        username: '未知用户',
                        avatar_url: null
                    });
                }
            }

            console.log('✅ 最终结果数量:', moments.length);

            // 获取总数
            const total = allMoments.length;

            console.log('✅ 获取精彩瞬间成功:', {
                count: moments.length,
                total: total
            });

            return res.json({
                moments,
                total: total,
                page: parseInt(page),
                limit: parseInt(limit)
            });

        } catch (queryError) {
            console.error('❌ 查询失败:', queryError);
            console.error('参数详情:', [gameId, parseInt(limit), offset]);

            // 如果查询失败，返回空结果
            return res.json({
                moments: [],
                total: 0,
                page: parseInt(page),
                limit: parseInt(limit),
                error: '查询失败: ' + queryError.message
            });
        }



    } catch (error) {
        console.error('❌ 获取精彩瞬间失败:', error);
        console.error('错误详情:', {
            message: error.message,
            code: error.code,
            sql: error.sql
        });
        res.status(500).json({ error: '获取精彩瞬间失败' });
    }
});

// 瞬间投票
app.post('/api/moments/:momentId/vote', requireAuthWithStatusCheck, async (req, res) => {
    try {
        const momentId = parseInt(req.params.momentId);
        const { voteType } = req.body;
        const userId = req.session.userId;

        if (isNaN(momentId) || !['like', 'dislike'].includes(voteType)) {
            return res.status(400).json({ error: '无效的参数' });
        }

        // 检查瞬间是否存在
        const [moments] = await promisePool.execute(
            'SELECT id FROM game_moments WHERE id = ? AND is_deleted = FALSE',
            [momentId]
        );

        if (moments.length === 0) {
            return res.status(404).json({ error: '瞬间不存在' });
        }

        // 检查是否已经投票
        const [existingVotes] = await promisePool.execute(
            'SELECT vote_type FROM moment_votes WHERE moment_id = ? AND user_id = ?',
            [momentId, userId]
        );

        if (existingVotes.length > 0) {
            if (existingVotes[0].vote_type === voteType) {
                // 取消投票
                await promisePool.execute(
                    'DELETE FROM moment_votes WHERE moment_id = ? AND user_id = ?',
                    [momentId, userId]
                );
            } else {
                // 更改投票
                await promisePool.execute(
                    'UPDATE moment_votes SET vote_type = ? WHERE moment_id = ? AND user_id = ?',
                    [voteType, momentId, userId]
                );
            }
        } else {
            // 新投票
            await promisePool.execute(
                'INSERT INTO moment_votes (moment_id, user_id, vote_type) VALUES (?, ?, ?)',
                [momentId, userId, voteType]
            );
        }

        // 更新瞬间的投票计数
        await updateMomentVoteCounts(momentId);

        // 创建投票通知（只有新投票才发送通知）
        if (existingVotes.length === 0) {
            const [moment] = await promisePool.execute(
                'SELECT user_id FROM game_moments WHERE id = ?',
                [momentId]
            );

            if (moment.length > 0 && moment[0].user_id !== userId) {
                const [voter] = await promisePool.execute(
                    'SELECT username FROM users WHERE id = ?',
                    [userId]
                );

                if (voter.length > 0) {
                    const voteText = voteType === 'like' ? '点赞' : '点踩';
                    await createNotification(
                        moment[0].user_id,
                        voteType,
                        `${voter[0].username} ${voteText}了您的精彩瞬间`,
                        `您的精彩瞬间获得了一个${voteText}`,
                        momentId,
                        'moment',
                        userId
                    );
                }
            }
        }

        res.json({ message: '投票成功' });

    } catch (error) {
        console.error('瞬间投票失败:', error);
        res.status(500).json({ error: '投票失败' });
    }
});

// 获取瞬间评论列表
app.get('/api/moments/:momentId/comments', async (req, res) => {
    try {
        const momentId = parseInt(req.params.momentId);
        const userId = req.session.userId;

        if (isNaN(momentId)) {
            return res.status(400).json({ error: '无效的瞬间ID' });
        }

        // 获取所有评论（包括回复）
        const [comments] = await promisePool.execute(`
            SELECT
                mc.id,
                mc.user_id,
                u.username,
                p.avatar_url,
                mc.content,
                mc.parent_id,
                mc.likes_count,
                mc.dislikes_count,
                mc.created_at,
                (SELECT COUNT(*) FROM moment_comments replies WHERE replies.parent_id = mc.id AND replies.is_deleted = 0) as replies_count
            FROM moment_comments mc
            JOIN users u ON mc.user_id = u.id
            LEFT JOIN user_profiles p ON u.id = p.user_id
            WHERE mc.moment_id = ? AND mc.is_deleted = 0
            ORDER BY mc.parent_id ASC, mc.created_at ASC
        `, [momentId]);

        // 如果用户已登录，获取用户的投票状态
        let userVotes = {};
        if (userId && comments.length > 0) {
            const placeholders = comments.map(() => '?').join(',');
            const [votes] = await promisePool.execute(
                `SELECT comment_id, vote_type
                 FROM moment_comment_votes
                 WHERE user_id = ? AND comment_id IN (${placeholders})`,
                [userId, ...comments.map(c => c.id)]
            );

            userVotes = votes.reduce((acc, vote) => {
                acc[vote.comment_id] = vote.vote_type;
                return acc;
            }, {});
        }

        // 组织评论层级结构
        const topLevelComments = comments.filter(c => !c.parent_id);
        const repliesMap = comments.filter(c => c.parent_id).reduce((acc, reply) => {
            if (!acc[reply.parent_id]) acc[reply.parent_id] = [];
            acc[reply.parent_id].push({
                ...reply,
                userVote: userVotes[reply.id] || null
            });
            return acc;
        }, {});

        const commentsWithReplies = topLevelComments.map(comment => ({
            ...comment,
            userVote: userVotes[comment.id] || null,
            replies: repliesMap[comment.id] || []
        }));

        res.json({ comments: commentsWithReplies });

    } catch (error) {
        console.error('获取瞬间评论失败:', error);
        res.status(500).json({ error: '获取评论失败' });
    }
});

// 发布瞬间评论
app.post('/api/moments/:momentId/comments', requireAuthWithStatusCheck, async (req, res) => {
    try {
        const momentId = parseInt(req.params.momentId);
        const userId = req.session.userId;
        const { content, parentId } = req.body;

        if (isNaN(momentId)) {
            return res.status(400).json({ error: '无效的瞬间ID' });
        }

        if (!content || content.trim().length === 0) {
            return res.status(400).json({ error: '评论内容不能为空' });
        }

        if (content.length > 500) {
            return res.status(400).json({ error: '评论内容不能超过500字符' });
        }

        // 检查瞬间是否存在
        const [moments] = await promisePool.execute(
            'SELECT id, user_id FROM game_moments WHERE id = ? AND is_deleted = 0',
            [momentId]
        );

        if (moments.length === 0) {
            return res.status(404).json({ error: '瞬间不存在' });
        }

        // 如果是回复，检查父评论是否存在
        if (parentId) {
            const [parentComments] = await promisePool.execute(
                'SELECT id FROM moment_comments WHERE id = ? AND moment_id = ? AND is_deleted = 0',
                [parentId, momentId]
            );

            if (parentComments.length === 0) {
                return res.status(404).json({ error: '要回复的评论不存在' });
            }
        }

        // 插入评论
        const [result] = await promisePool.execute(
            'INSERT INTO moment_comments (user_id, moment_id, content, parent_id) VALUES (?, ?, ?, ?)',
            [userId, momentId, content.trim(), parentId || null]
        );

        // 更新瞬间的评论数量
        await promisePool.execute(
            'UPDATE game_moments SET comments_count = (SELECT COUNT(*) FROM moment_comments WHERE moment_id = ? AND is_deleted = 0) WHERE id = ?',
            [momentId, momentId]
        );

        // 创建评论通知
        if (parentId) {
            // 回复评论的通知
            const [parentComment] = await promisePool.execute(
                'SELECT user_id FROM moment_comments WHERE id = ?',
                [parentId]
            );

            if (parentComment.length > 0 && parentComment[0].user_id !== userId) {
                const [replier] = await promisePool.execute(
                    'SELECT username FROM users WHERE id = ?',
                    [userId]
                );

                if (replier.length > 0) {
                    await createNotification(
                        parentComment[0].user_id,
                        'reply',
                        `${replier[0].username} 回复了您的瞬间评论`,
                        `回复内容：${content.length > 100 ? content.substring(0, 100) + '...' : content}`,
                        result.insertId,
                        'moment_comment',
                        userId
                    );
                }
            }
        } else {
            // 评论瞬间的通知
            if (moments[0].user_id !== userId) {
                const [commenter] = await promisePool.execute(
                    'SELECT username FROM users WHERE id = ?',
                    [userId]
                );

                if (commenter.length > 0) {
                    await createNotification(
                        moments[0].user_id,
                        'comment',
                        `${commenter[0].username} 评论了您的精彩瞬间`,
                        `评论内容：${content.length > 100 ? content.substring(0, 100) + '...' : content}`,
                        result.insertId,
                        'moment_comment',
                        userId
                    );
                }
            }
        }

        res.status(201).json({
            message: '评论发布成功',
            commentId: result.insertId
        });

    } catch (error) {
        console.error('发布瞬间评论失败:', error);
        res.status(500).json({ error: '发布评论失败' });
    }
});

// 瞬间评论投票
app.post('/api/moments/comments/:commentId/vote', requireAuthWithStatusCheck, async (req, res) => {
    try {
        const commentId = parseInt(req.params.commentId);
        const userId = req.session.userId;
        const { voteType } = req.body;

        if (isNaN(commentId) || !['like', 'dislike'].includes(voteType)) {
            return res.status(400).json({ error: '无效的参数' });
        }

        // 检查评论是否存在
        const [comments] = await promisePool.execute(
            'SELECT id FROM moment_comments WHERE id = ? AND is_deleted = 0',
            [commentId]
        );

        if (comments.length === 0) {
            return res.status(404).json({ error: '评论不存在' });
        }

        // 检查是否已经投票
        const [existingVotes] = await promisePool.execute(
            'SELECT vote_type FROM moment_comment_votes WHERE comment_id = ? AND user_id = ?',
            [commentId, userId]
        );

        if (existingVotes.length > 0) {
            if (existingVotes[0].vote_type === voteType) {
                // 取消投票
                await promisePool.execute(
                    'DELETE FROM moment_comment_votes WHERE comment_id = ? AND user_id = ?',
                    [commentId, userId]
                );
                res.json({ message: '投票已取消', action: 'removed' });
            } else {
                // 更改投票类型
                await promisePool.execute(
                    'UPDATE moment_comment_votes SET vote_type = ? WHERE comment_id = ? AND user_id = ?',
                    [voteType, commentId, userId]
                );
                res.json({ message: '投票已更新', action: 'updated' });
            }
        } else {
            // 新增投票
            await promisePool.execute(
                'INSERT INTO moment_comment_votes (comment_id, user_id, vote_type) VALUES (?, ?, ?)',
                [commentId, userId, voteType]
            );
            res.json({ message: '投票成功', action: 'added' });
        }

        // 更新评论投票计数
        await updateMomentCommentVoteCounts(commentId);

        // 创建投票通知（只有新投票才发送通知）
        if (existingVotes.length === 0) {
            const [comment] = await promisePool.execute(
                'SELECT user_id FROM moment_comments WHERE id = ?',
                [commentId]
            );

            if (comment.length > 0 && comment[0].user_id !== userId) {
                const [voter] = await promisePool.execute(
                    'SELECT username FROM users WHERE id = ?',
                    [userId]
                );

                if (voter.length > 0) {
                    const voteText = voteType === 'like' ? '点赞' : '点踩';
                    await createNotification(
                        comment[0].user_id,
                        voteType,
                        `${voter[0].username} ${voteText}了您的瞬间评论`,
                        `您的瞬间评论获得了一个${voteText}`,
                        commentId,
                        'moment_comment',
                        userId
                    );
                }
            }
        }

    } catch (error) {
        console.error('瞬间评论投票失败:', error);
        res.status(500).json({ error: '投票失败' });
    }
});

// 更新瞬间评论投票计数的辅助函数
async function updateMomentCommentVoteCounts(commentId) {
    try {
        const [likesCount] = await promisePool.execute(
            'SELECT COUNT(*) as count FROM moment_comment_votes WHERE comment_id = ? AND vote_type = "like"',
            [commentId]
        );

        const [dislikesCount] = await promisePool.execute(
            'SELECT COUNT(*) as count FROM moment_comment_votes WHERE comment_id = ? AND vote_type = "dislike"',
            [commentId]
        );

        await promisePool.execute(
            'UPDATE moment_comments SET likes_count = ?, dislikes_count = ? WHERE id = ?',
            [likesCount[0].count, dislikesCount[0].count, commentId]
        );

        console.log(`✅ 更新瞬间评论 ${commentId} 投票计数: 点赞 ${likesCount[0].count}, 点踩 ${dislikesCount[0].count}`);

    } catch (error) {
        console.error('更新瞬间评论投票计数错误:', error);
    }
}

// 举报瞬间
app.post('/api/moments/:momentId/report', requireAuthWithStatusCheck, async (req, res) => {
    try {
        const momentId = parseInt(req.params.momentId);
        const userId = req.session.userId;
        const { reportType, reason } = req.body;

        if (isNaN(momentId)) {
            return res.status(400).json({ error: '无效的瞬间ID' });
        }

        const validReportTypes = ['spam', 'harassment', 'inappropriate', 'fake', 'copyright', 'other'];
        if (!validReportTypes.includes(reportType)) {
            return res.status(400).json({ error: '无效的举报类型' });
        }

        // 检查瞬间是否存在
        const [moments] = await promisePool.execute(
            'SELECT id FROM game_moments WHERE id = ? AND is_deleted = 0',
            [momentId]
        );

        if (moments.length === 0) {
            return res.status(404).json({ error: '瞬间不存在' });
        }

        // 检查是否已经举报过
        const [existingReports] = await promisePool.execute(
            'SELECT id FROM moment_reports WHERE moment_id = ? AND reporter_user_id = ?',
            [momentId, userId]
        );

        if (existingReports.length > 0) {
            return res.status(400).json({ error: '您已经举报过这个瞬间了' });
        }

        // 插入举报记录
        const [reportResult] = await promisePool.execute(
            'INSERT INTO moment_reports (moment_id, reporter_user_id, report_type, reason) VALUES (?, ?, ?, ?)',
            [momentId, userId, reportType, reason || null]
        );

        // 创建举报通知给管理员
        const [admins] = await promisePool.execute(
            'SELECT id FROM users WHERE role = "admin"'
        );

        const [reporter] = await promisePool.execute(
            'SELECT username FROM users WHERE id = ?',
            [userId]
        );

        if (admins.length > 0 && reporter.length > 0) {
            const reportTypeText = {
                'spam': '垃圾信息',
                'harassment': '骚扰辱骂',
                'inappropriate': '不当内容',
                'fake': '虚假信息',
                'copyright': '版权问题',
                'other': '其他'
            }[reportType] || reportType;

            // 给所有管理员发送通知
            for (const admin of admins) {
                await createNotification(
                    admin.id,
                    'admin_alert',
                    `新的精彩瞬间举报`,
                    `用户 ${reporter[0].username} 举报了一个精彩瞬间，举报类型：${reportTypeText}${reason ? `，原因：${reason}` : ''}`,
                    reportResult.insertId,
                    'moment_report',
                    userId
                );
            }
        }

        res.json({ message: '举报提交成功，我们会尽快处理' });

    } catch (error) {
        console.error('举报瞬间失败:', error);
        res.status(500).json({ error: '举报失败' });
    }
});

// 删除瞬间（仅限发布者）
app.delete('/api/moments/:momentId', requireAuthWithStatusCheck, async (req, res) => {
    try {
        const momentId = parseInt(req.params.momentId);
        const userId = req.session.userId;

        if (isNaN(momentId)) {
            return res.status(400).json({ error: '无效的瞬间ID' });
        }

        // 检查瞬间是否存在且属于当前用户
        const [moments] = await promisePool.execute(
            'SELECT id, user_id FROM game_moments WHERE id = ? AND is_deleted = 0',
            [momentId]
        );

        if (moments.length === 0) {
            return res.status(404).json({ error: '瞬间不存在' });
        }

        if (moments[0].user_id !== userId) {
            return res.status(403).json({ error: '您只能删除自己发布的瞬间' });
        }

        // 软删除瞬间
        await promisePool.execute(
            'UPDATE game_moments SET is_deleted = 1 WHERE id = ?',
            [momentId]
        );

        res.json({ message: '瞬间删除成功' });

    } catch (error) {
        console.error('删除瞬间失败:', error);
        res.status(500).json({ error: '删除失败' });
    }
});



// 更新瞬间投票计数的辅助函数
async function updateMomentVoteCounts(momentId) {
    try {
        const [likesResult] = await promisePool.execute(
            'SELECT COUNT(*) as count FROM moment_votes WHERE moment_id = ? AND vote_type = "like"',
            [momentId]
        );

        const [dislikesResult] = await promisePool.execute(
            'SELECT COUNT(*) as count FROM moment_votes WHERE moment_id = ? AND vote_type = "dislike"',
            [momentId]
        );

        const likesCount = likesResult[0].count;
        const dislikesCount = dislikesResult[0].count;

        await promisePool.execute(
            'UPDATE game_moments SET likes_count = ?, dislikes_count = ? WHERE id = ?',
            [likesCount, dislikesCount, momentId]
        );

        console.log(`✅ 更新瞬间 ${momentId} 投票计数: 点赞 ${likesCount}, 点踩 ${dislikesCount}`);

        return { likesCount, dislikesCount };
    } catch (error) {
        console.error('更新瞬间投票计数失败:', error);
        throw error;
    }
}

// 创建通知的辅助函数
async function createNotification(userId, type, title, content, relatedId = null, relatedType = null, senderId = null) {
    try {
        console.log('🔔 准备创建通知:', {
            userId, type, title, content, relatedId, relatedType, senderId
        });

        await promisePool.execute(`
            INSERT INTO notifications (user_id, type, title, content, related_id, related_type, sender_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [userId, type, title, content, relatedId, relatedType, senderId]);

        console.log(`✅ 通知已创建: ${title} -> 用户${userId}`);
    } catch (error) {
        console.error('❌ 创建通知失败:', error);
        console.error('❌ 错误详情:', error.message);
        console.error('❌ 参数:', { userId, type, title, content, relatedId, relatedType, senderId });
    }
}

// 启动服务器
console.log('🔄 正在启动服务器...');
console.log('📍 PORT值:', PORT);

app.listen(PORT, () => {
    console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
    console.log('📁 静态文件目录:', path.join(__dirname, '../frontend'));
    testConnection();
}).on('error', (err) => {
    console.error('❌ 服务器启动失败:', err);
});

module.exports = app;
