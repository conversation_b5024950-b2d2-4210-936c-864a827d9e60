<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏图片测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .game-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .game-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .game-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .game-platform {
            color: #666;
            font-size: 14px;
        }
        .error {
            color: red;
            background-color: #ffe6e6;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>游戏图片测试页面</h1>
        <p>这个页面用于测试所有游戏图片是否能正确加载</p>
        
        <div id="loading" class="loading">正在加载游戏数据...</div>
        <div id="error" class="error" style="display: none;"></div>
        <div id="games-container" class="games-grid"></div>
    </div>

    <script>
        async function loadGames() {
            try {
                const response = await fetch('/api/games');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                displayGames(data.games);
            } catch (error) {
                console.error('加载游戏数据失败:', error);
                document.getElementById('error').textContent = `加载失败: ${error.message}`;
                document.getElementById('error').style.display = 'block';
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        function displayGames(games) {
            const container = document.getElementById('games-container');
            container.innerHTML = '';

            games.forEach(game => {
                const gameCard = document.createElement('div');
                gameCard.className = 'game-card';
                
                gameCard.innerHTML = `
                    <img src="${game.cover_url}" alt="${game.name}" class="game-image" 
                         onerror="this.style.backgroundColor='#f0f0f0'; this.style.display='flex'; this.style.alignItems='center'; this.style.justifyContent='center'; this.innerHTML='图片加载失败';">
                    <div class="game-title">${game.name}</div>
                    <div class="game-platform">${game.platform}</div>
                    <div style="font-size: 12px; color: #999; margin-top: 5px;">
                        图片路径: ${game.cover_url}
                    </div>
                `;
                
                container.appendChild(gameCard);
            });
        }

        // 页面加载完成后获取游戏数据
        document.addEventListener('DOMContentLoaded', loadGames);
    </script>
</body>
</html>
